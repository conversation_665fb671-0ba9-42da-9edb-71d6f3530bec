@import "@core/scss/core.scss";
@import "./assets/scss/styles";
@import "./assets/css/google_font.css";
@import "./assets/css/google_icons.css";
@import "@angular/cdk/overlay-prebuilt.css";
@import "primeicons/primeicons.css";

* {
  font-family: Arial, sans-serif !important;
}
html,
body {
  height: 100%;
  color: rgba(0, 0, 0, 0.699);
  font-size: 13px;
}
body {
  margin: 0;
  font-family: Arial, sans-serif;
}
mark {
  background-color: rgb(255, 255, 0);
  color: #000;
}
.pi {
  font-family: "primeicons" !important;
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  text-align: center;
}
::-webkit-scrollbar {
  width: 6px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #c4c4c47c;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #a5a5a579;
  cursor: pointer;
}
div.cdk-overlay-container {
  z-index: 2000 !important;
}
.datatable-body-cell-label {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.d2h-code-line,
.d2h-code-line-ctn {
  white-space: pre-wrap !important;
}
.d2h-code-line-prefix {
  display: none !important;
}
.d2h-moved-tag {
  display: none !important;
}
.d2h-code-side-line {
  width: 100% !important;
  padding-right: 0 !important;
}

.font-lg {
  font-size: 20px !important;
  @media screen and (max-width: 1024px) {
    font-size: 15px !important;
  }
  @media screen and (max-width: 1300px) {
    font-size: 13px !important;
  }
  @media screen and (max-width: 992px) {
    font-size: 10px !important;
  }
}
.font-sm {
  font-size: 1rem !important;
  @media screen and (max-width: 1024px) {
    font-size: 0.75rem !important;
  }
  @media screen and (max-width: 1300px) {
    font-size: 0.7rem !important;
  }
}
.font-xs {
  font-size: 0.8rem !important;
  @media screen and (max-width: 1024px) {
    font-size: 0.7rem !important;
  }
  @media screen and (max-width: 1300px) {
    font-size: 0.6rem !important;
  }
}
.font-xl {
  font-size: 30px !important;
  @media screen and (max-width: 1024px) {
    font-size: 20px !important;
  }
  @media screen and (max-width: 1300px) {
    font-size: 1.2rem !important;
  }
}
.font-bold-500 {
  font-weight: 500;
}
.colorBG {
  background: rgb(230, 236, 248);
}
.apexcharts-legend {
  justify-content: start !important;
}
// tr:hover {
//   background-color: #50beda7a; /* Màu nền nhạt */
//   cursor: pointer;
// }
ngx-extended-pdf-viewer .unverified-signature-warning {
  display: none !important;
}
.btn:disabled {
  cursor: not-allowed;
}
.ngx-datatable.bootstrap.core-bootstrap.cursor
  .datatable-body-row.datatable-row-even:hover {
  background-color: #50beda7a !important;
}
.ngx-datatable.bootstrap.core-bootstrap.cursor .datatable-body-row:hover {
  background-color: #50beda7a !important;
}
.wrap-text {
  word-wrap: break-word; /* Ngắt từ khi cần */
  white-space: pre-wrap;
}
ngx-datatable {
  font-size: 14px !important;
}
ngx-datatable .datatable-header-cell {
  font-size: 14px !important;
  font-weight: bold;
}
th {
  font-weight: bold;
  color: rgba(0, 0, 0, 0.699);
  font-size: 14px !important;
}
.label {
  font-size: 13px !important;
}
label {
  font-size: 13px !important;
  color: #333;
}
.w-90 {
  width: 90%;
}
.w-80 {
  width: 80%;
}
.w-10 {
  width: 10%;
}
.w-20 {
  width: 20%;
}
.w-85 {
  width: 85%;
}
.w-15 {
  width: 15%;
}
hr {
  margin: 0;
}
.single-line {
  white-space: nowrap; /* Ngăn văn bản xuống dòng */
  overflow: hidden; /* Ẩn phần văn bản vượt quá */
  text-overflow: ellipsis; /* Thêm dấu "..." vào cuối dòng */
}
.two-line {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4em;
  min-height: 2.8em; /* đảm bảo đủ chỗ cho 2 dòng */
}
.one-line {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4em;
  min-height: 1.4em; /* đảm bảo đủ chỗ cho 2 dòng */
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.icon-button:hover {
  background-color: rgba(238, 238, 238, 1);
}
.x-button {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}
.show-button-label {
  margin-left: 8px;
}
.show-button-toggle {
  border: 1px solid rgba(216, 214, 222, 1);
  border-radius: 5px;
  padding: 5px;
}

/* Ẩn chữ khi màn hình nhỏ hơn 1280px */
@media (max-width: 1280px) {
  .show-button-label {
    display: none;
  }
  .show-button-toggle {
    border: none;
  }
}
.datatable-header {
  width: 100% !important;
}
.datatable-body {
  width: 100% !important;
}
.no-select {
  user-select: none;
  -webkit-user-drag: none; /* Safari */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
.ng-select.is-invalid .ng-select-container {
  border-color: #ff0000;
}
.container {
  width: calc(100vw - (100vw - 100%) - calc(2rem * 2));
}
.swal-confirm {
  color: white !important;
}
.swal-cancel {
  color: black !important;
}


#toast-container .toast-success.toast-blue-icon::before {
  background-color: transparent !important;
  background-image: url("assets/images/icons/icons8-success.svg") !important;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 25px 25px;
}

#toast-container .toast-title-blue { color: #5C7CFA !important; }

.overlay-in-modal.p-multiselect-panel {
  z-index: 3000 !important;
}

.p-dropdown-panel,
.p-autocomplete-panel,
.p-datepicker,
.p-overlaypanel {
  z-index: 3000 !important;
}

.on-top {
  z-index: 9999 !important;
}

// Utility classes for margin, padding, height, bottom, top, right, left
@for $i from -200 through 200 {
  .mt-#{$i}px { margin-top: #{$i}px !important; }
  .mb-#{$i}px { margin-bottom: #{$i}px !important; }
  .mr-#{$i}px { margin-right: #{$i}px !important; }
  .ml-#{$i}px { margin-left: #{$i}px !important; }

  .pt-#{$i}px { padding-top: #{$i}px !important; }
  .pb-#{$i}px { padding-bottom: #{$i}px !important; }
  .pr-#{$i}px { padding-right: #{$i}px !important; }
  .pl-#{$i}px { padding-left: #{$i}px !important; }

  .height-#{$i}px { height: #{$i}px !important; }
  .width-#{$i}px { width: #{$i}px !important; }

  .height-#{$i}vh { height: #{$i}vh !important; }
  .width-#{$i}vw { width: #{$i}vw !important; }

  .height-#{$i}percent { height: #{$i}#{'%'} !important; }
  .width-#{$i}percent { width: #{$i}#{'%'} !important; }

  .min-height-#{$i}px { min-height: #{$i}px !important; }
  .min-width-#{$i}px { min-width: #{$i}px !important; }

  .max-height-#{$i}vh { max-height: #{$i}vh !important; }
  .max-width-#{$i}vw { max-width: #{$i}vw !important; }

  .bottom-#{$i}px { bottom: #{$i}px !important; }
  .top-#{$i}px { top: #{$i}px !important; }
  .right-#{$i}px { right: #{$i}px !important; }
  .left-#{$i}px { left: #{$i}px !important; }

  .gap-#{$i}px { gap: #{$i}px !important; }
  .border-radius-#{$i}px { border-radius: #{$i}px !important; }
}

/* Login background image */
.login-bg {
  background: url('assets/images/illustration/loginBG.png');
  background-size: cover;
  background-position: center;
}

.color-333 {
  color: #333 !important;
}

.shadow-avatar {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.text-wrap-pretty {
  text-wrap: pretty !important;
}

#loading-bg {
  width: 100%;
  height: 100%;
  background: #fff;
  display: block;
  position: absolute;
  z-index: 99999;
  pointer-events: none;
}

.loading-logo {
  position: absolute;
  height: 200px;
  left: 50%;
  top: 30vh;
  transform: translateX(-50%);
  img {
    height: 25vh
  }
}

.loading {
  position: absolute;
  left: 50%;
  top: calc(30vh + 200px); /* 200px (height logo) + 20px khoảng cách */
  transform: translateX(-50%);
  width: 55px;
  height: 55px;
  border-radius: 50%;
  box-sizing: border-box;
  border: 3px solid transparent;
}

.loading .effect-1,
.loading .effect-2 {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-left: 3px solid #008fe3;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.loading .effect-1 {
  animation: rotate 1s ease infinite;
}

.loading .effect-2 {
  animation: rotateOpacity 1s ease infinite 0.1s;
}

.loading .effect-3 {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-left: 3px solid #008fe3;
  -webkit-animation: rotateOpacity 1s ease infinite 0.2s;
  animation: rotateOpacity 1s ease infinite 0.2s;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.loading .effects {
  transition: all 0.3s ease;
}

@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}

@keyframes rotateOpacity {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    opacity: 0.1;
  }

  100% {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
    opacity: 1;
  }
}


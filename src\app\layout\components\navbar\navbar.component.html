<div *ngIf="horizontalMenu" class="navbar-header d-xl-block d-none">
  <!-- Navbar brand -->
  <ul class="nav navbar-nav flex-row">
    <li class="nav-item">
      <a class="navbar-brand" [routerLink]="['/']">
        <span class="brand-logo">
          <img
            src="{{ coreConfig.app.appLogoImage }}"
            alt="brand-logo"
            width="36"
          />
        </span>
        <h2 class="brand-text mb-0">{{ coreConfig.app.appName }}</h2>
      </a>
    </li>
  </ul>
  <!--/ Navbar brand -->
</div>
<div class="d-xl-flex d-none align-items-center w-100" *ngIf="role == 'USER'">
  <!-- Navbar brand -->
  <img
    class="navbar-logo-cmc"
    src="assets/images/logo/CMC_ATI.svg"
    alt="CMC_ATI"
  />
  <span class="mx-1 navbar-divider"></span>
  <h4 class="mb-0 text-primary navbar-title-custom">
    {{ coreConfig.app.appTitle }}
  </h4>
  <!--/ Navbar brand -->
</div>
<!--
  background-image: url('assets/images/logo/navbar.svg');
  background-size: cover;
  background-position: center;
" -->
<div class="navbar-container d-flex content">
  <div class="bookmark-wrapper d-flex align-items-center">
    <!-- Menu Toggler | Menu icon will be hidden in case of layout without menu -->
    <ul class="nav navbar-nav d-xl-none" *ngIf="!coreConfig.layout.menu.hidden">
      <li class="nav-item">
        <a class="nav-link menu-toggle" (click)="toggleSidebar('menu')">
          <span [data-feather]="'menu'" [class]="'ficon'"></span>
        </a>
      </li>
    </ul>
    <!--/ Menu Toggler -->

    <!-- Toggle skin -->
    <!-- <li class="nav-item d-none d-lg-block">
      <a
        type="button"
        class="nav-link nav-link-style btn"
        (click)="toggleDarkSkin()"
      >
        <span
          [ngClass]="currentSkin === 'dark' ? 'icon-sun' : 'icon-moon'"
          class="ficon font-medium-5 feather"
        ></span>
      </a>
    </li> -->
    <!--/ Toggle skin -->
  </div>

  <ul class="nav navbar-nav align-items-center ml-auto">
    <!-- ? Language selection | Uncomment if needed-->
    <!-- <li ngbDropdown class="nav-item dropdown dropdown-language">
      <a class="nav-link dropdown-toggle" id="dropdown-flag" ngbDropdownToggle>
        <i
          class="flag-icon flag-icon-{{
            languageOptions[_translateService.currentLang].flag
          }}"
        ></i
        ><span class="selected-language">{{
          languageOptions[_translateService.currentLang].title
        }}</span></a
      >
      <div ngbDropdownMenu aria-labelledby="dropdown-flag">
        <a
          *ngFor="let lang of _translateService.getLangs()"
          ngbDropdownItem
          (click)="setLanguage(lang)"
        >
          <i class="flag-icon flag-icon-{{ languageOptions[lang].flag }}"></i>
          {{ languageOptions[lang].title }}
        </a>
      </div>
    </li> -->
    <!--/ Language selection -->

    <!-- User Dropdown -->
    <li ngbDropdown class="nav-item dropdown-user">
      <a
        class="nav-link dropdown-toggle dropdown-user-link"
        id="dropdown-user"
        ngbDropdownToggle
        id="navbarUserDropdown"
        aria-haspopup="true"
        aria-expanded="false"
      >
        <div class="user-nav d-sm-flex d-none">
          <span class="user-name font-weight-bolder">{{
            userName == "" ? "CLS User" : userName
          }}</span>
          <span class="user-status">{{
            role === "ADMIN"
              ? "ADMIN"
              : role === "SUPER_ADMIN"
              ? "SUPER_ADMIN"
              : "USER"
          }}</span>
        </div>

        <span class="avatar"
          ><img
            class="round"
            [src]="
              currentUser?.avatar
                ? currentUser?.avatar
                : 'assets/images/portrait/small/users.png'
            "
            (error)="refreshLinkAvatar($event)"
            alt="avatar"
            height="40"
            width="40" /><span class="avatar-status-online"></span
        ></span>
      </a>
      <div
        ngbDropdownMenu
        aria-labelledby="navbarUserDropdown"
        class="dropdown-menu dropdown-menu-right"
      >
        <a ngbDropdownItem (click)="editUser()"
          ><span [data-feather]="'user'" [class]="'mr-50'"></span> Thông tin
        </a>
        <a
          ngbDropdownItem
          (click)="addAccount()"
          *ngIf="role === 'ADMIN' || role == 'SUPER_ADMIN'"
          ><span [data-feather]="'user-plus'" [class]="'mr-50'"></span>
          {{ "AddAccount" | translate }}</a
        >
        <a ngbDropdownItem (click)="changePass()"
          ><span [data-feather]="'lock'" [class]="'mr-50'"></span> Đổi mật
          khẩu</a
        >
        <a
          *ngIf="role == 'SUPER_ADMIN'"
          ngbDropdownItem
          [routerLink]="['/cau-hinh']"
          ><span [data-feather]="'settings'" [class]="'mr-50'"></span> Cấu
          hình</a
        >
        <a ngbDropdownItem (click)="report()"
          ><span [data-feather]="'alert-triangle'" [class]="'mr-50'"></span> Báo
          cáo</a
        >
        <!-- <a ngbDropdownItem [routerLink]="['/']"
          ><span [data-feather]="'message-square'" [class]="'mr-50'"></span>
          Chats</a
        > -->
        <!-- <div class="dropdown-divider"></div> -->
        <a
          (click)="logout()"
          ngbDropdownItem
          [routerLink]="['/pages/authentication/login-v2']"
          ><span [data-feather]="'power'" [class]="'mr-50'"></span>
          {{ "Logout" | translate }}</a
        >
      </div>
    </li>
    <!--/ User Dropdown -->
  </ul>
</div>
<ng-template #changePassModal let-modal>
  <div class="modal-body" tabindex="0" ngbAutofocus>
    <div class="form-group">
      <label
        for="basicTextarea"
        class="w-100 align-items-center d-flex justify-content-between"
        >Đổi mật khẩu
        <div class="">
          <button
            class="btn btn-sm ml-auto p-0"
            (click)="modal.dismiss('Cross click')"
          >
            <img src="assets/images/icons/x.svg" alt="x" />
          </button></div
      ></label>
    </div>
    <form [formGroup]="formChangePass" (ngSubmit)="onSubmit()">
      <div class="row">
        <div class="col-12">
          <div class="form-group">
            <label for="currentPassword">Mật khẩu cũ</label>
            <div class="input-group form-password-toggle mb-50">
              <input
                [type]="showOldPass ? 'text' : 'password'"
                placeholder="Nhập mật khẩu cũ"
                formControlName="current_password"
                class="form-control"
                id="basic-default-password"
                aria-describedby="basic-default-password"
              />
              <div
                class="input-group-append"
                (click)="showOldPass = !showOldPass"
              >
                <span class="input-group-text cursor-pointer"
                  ><i
                    class="feather"
                    [ngClass]="{
                      'icon-eye-off': showOldPass,
                      'icon-eye': !showOldPass
                    }"
                  ></i
                ></span>
              </div>
            </div>
            <div
              class="text-danger"
              *ngIf="
                fc.current_password.invalid &&
                (fc.current_password.dirty || fc.current_password.touched)
              "
            >
              <div *ngIf="fc.current_password.errors?.required">
                Mật khẩu cũ là bắt buộc.
              </div>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="form-group">
            <label for="newPassword">Mật khẩu mới</label>

            <div class="input-group form-password-toggle mb-50">
              <input
                [type]="showNewPass ? 'text' : 'password'"
                placeholder="Nhập mật khẩu mới"
                formControlName="new_password"
                class="form-control"
                id="basic-default-password"
                aria-describedby="basic-default-password"
              />
              <div
                class="input-group-append"
                (click)="showNewPass = !showNewPass"
              >
                <span class="input-group-text cursor-pointer"
                  ><i
                    class="feather"
                    [ngClass]="{
                      'icon-eye-off': showNewPass,
                      'icon-eye': !showNewPass
                    }"
                  ></i
                ></span>
              </div>
            </div>
            <div
              class="text-danger"
              *ngIf="
                fc.new_password.invalid &&
                (fc.new_password.dirty || fc.new_password.touched)
              "
            >
              <div *ngIf="fc.new_password.errors?.required">
                Mật khẩu mới là bắt buộc.
              </div>

              <div *ngIf="fc.new_password.errors?.minlength">
                Mật khẩu mới phải có ít nhất 6 ký tự.
              </div>

              <div *ngIf="fc.new_password.errors?.pattern">
                Mật khẩu phải có ít nhất 1 chữ hoa, 1 số và 1 ký tự đặc biệt.
              </div>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="form-group">
            <label for="confirmPassword">Xác nhận mật khẩu</label>

            <div class="input-group form-password-toggle mb-50">
              <input
                [type]="showConfirmPass ? 'text' : 'password'"
                placeholder="Xác nhận mật khẩu mới"
                formControlName="confirm_password"
                class="form-control"
                id="confirm-password"
                aria-describedby="confirm-password"
              />
              <div
                class="input-group-append"
                (click)="showConfirmPass = !showConfirmPass"
              >
                <span class="input-group-text cursor-pointer">
                  <i
                    class="feather"
                    [ngClass]="{
                      'icon-eye-off': showConfirmPass,
                      'icon-eye': !showConfirmPass
                    }"
                  ></i>
                </span>
              </div>
            </div>

            <div
              class="text-danger"
              *ngIf="
                fc.confirm_password.invalid &&
                (fc.confirm_password.dirty || fc.confirm_password.touched)
              "
            >
              <div *ngIf="fc.confirm_password.errors?.required">
                Xác nhận mật khẩu là bắt buộc.
              </div>
              <div *ngIf="fc.confirm_password.errors?.mustMatch">
                Mật khẩu xác nhận không khớp.
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      rippleEffect
      class="btn btn-secondary mr-1"
      (click)="modal.close('Cross click')"
    >
      Huỷ
    </button>
    <button
      rippleEffect
      class="btn btn-primary"
      (click)="submitChangePass()"
      [disabled]="!formChangePass.valid"
    >
      Xác nhận
    </button>
  </div>
</ng-template>
<ng-template #addAccountModal let-modal>
  <div class="modal-body" tabindex="0" ngbAutofocus>
    <div class="form-group">
      <label
        for="basicTextarea"
        class="w-100 align-items-center d-flex justify-content-between"
        >Thêm tài khoản
        <div class="">
          <button
            class="btn btn-sm ml-auto p-0"
            (click)="modal.dismiss('Cross click')"
          >
            <img src="assets/images/icons/x.svg" alt="x" />
          </button></div
      ></label>
    </div>
    <form [formGroup]="formAddAccount" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="fullName">Họ và tên</label>
        <input
          type="text"
          class="form-control"
          placeholder="Họ và tên"
          formControlName="fullName"
        />
        <div
          class="text-danger"
          *ngIf="
            getFormControl('fullName').invalid &&
            getFormControl('fullName').touched
          "
        >
          <div *ngIf="getFormControl('fullName').errors?.required">
            Không được bỏ trống
          </div>
        </div>
      </div>
      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="text"
          class="form-control"
          placeholder="Email"
          formControlName="email"
        />
        <div
          class="text-danger"
          *ngIf="
            getFormControl('email').invalid && getFormControl('email').touched
          "
        >
          <div *ngIf="getFormControl('email').errors?.required">
            Không được bỏ trống
          </div>
          <div *ngIf="getFormControl('email').errors?.email">
            Email sai định dạng
          </div>
        </div>
      </div>
      <div class="form-group">
        <label for="password">Mật khẩu</label>

        <div class="input-group form-password-toggle mb-50">
          <input
            [type]="showPassWord ? 'text' : 'password'"
            placeholder="Mật khẩu"
            formControlName="password"
            class="form-control"
            id="basic-default-password"
            aria-describedby="basic-default-password"
          />
          <div
            class="input-group-append"
            (click)="showPassWord = !showPassWord"
          >
            <span class="input-group-text cursor-pointer"
              ><i
                class="feather"
                [ngClass]="{
                  'icon-eye-off': showPassWord,
                  'icon-eye': !showPassWord
                }"
              ></i
            ></span>
          </div>
        </div>
        <div
          class="text-danger"
          *ngIf="
            getFormControl('password').invalid &&
            getFormControl('password').touched
          "
        >
          <div *ngIf="getFormControl('password').errors?.required">
            Không được bỏ trống
          </div>
          <div *ngIf="getFormControl('password').errors?.minlength">
            Mật khẩu ít nhất 6 ký tự
          </div>
          <div *ngIf="getFormControl('password').errors?.pattern">
            Mật khẩu phải có ít nhất 1 chữ hoa, 1 số và 1 ký tự đặc biệt.
          </div>
        </div>
      </div>
      <div class="form-group">
        <label for="confirmPassword">Xác nhận mật khẩu</label>

        <div class="input-group form-password-toggle mb-50">
          <input
            [type]="showConfirmPass ? 'text' : 'password'"
            placeholder="Xác nhận mật khẩu mới"
            formControlName="confirm_password"
            class="form-control"
            id="confirm-password"
            aria-describedby="confirm-password"
          />
          <div
            class="input-group-append"
            (click)="showConfirmPass = !showConfirmPass"
          >
            <span class="input-group-text cursor-pointer">
              <i
                class="feather"
                [ngClass]="{
                  'icon-eye-off': showConfirmPass,
                  'icon-eye': !showConfirmPass
                }"
              ></i>
            </span>
          </div>
        </div>

        <div
          class="text-danger"
          *ngIf="
            getFormControl('confirm_password').invalid &&
            (getFormControl('confirm_password').dirty ||
              getFormControl('confirm_password').touched)
          "
        >
          <div *ngIf="getFormControl('confirm_password').errors?.required">
            Xác nhận mật khẩu là bắt buộc.
          </div>
          <div *ngIf="getFormControl('confirm_password').errors?.mustMatch">
            Mật khẩu xác nhận không khớp.
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button rippleEffect class="btn btn-secondary mr-1" (click)="modal.close()">
      Huỷ
    </button>
    <button
      rippleEffect
      class="btn btn-primary"
      (click)="submitAddAccount()"
      [disabled]="!formAddAccount.valid"
    >
      Xác nhận
    </button>
  </div>
</ng-template>
<ng-template #reportModal let-modal>
  <app-report [modal]="modal"></app-report>
</ng-template>
<ng-template #editUserModal let-modal>
  <app-user-profile [modal]="modal"></app-user-profile>
</ng-template>

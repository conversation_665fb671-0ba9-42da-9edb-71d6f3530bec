<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumbDefault"></app-breadcrumb>
  <div class="card">
    <div class="card-body">
      <form
        [formGroup]="campaignForm"
        autocomplete="off"
        (ngSubmit)="onSubmit()"
      >
        <div class="d-flex">
          <div class="col-lg-4 col-12 pr-1 border-end">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <h4 class="mb-0 fw-bold">
                Danh sách người nhận ({{ filteredRecipients.length }})
              </h4>
              <button
                *ngIf="![campaignStatus.STATUS_ONGOING, campaignStatus.STATUS_DONE].includes(status)"
                type="button"
                class="btn btn-sm btn-primary d-inline-flex align-items-center"
                aria-label="Thêm người nhận"
                title="Thêm ngườ<PERSON> nhận"
                (click)="onAddRecipient()"
              >
                <i data-feather="plus" class="mr-25"></i>
                <span>Thêm người nhận</span>
              </button>
            </div>

            <!-- Search -->
            <label>Tìm kiếm</label>
            <div class="input-with-icon position-relative">
              <button
                type="button"
                class="btn-icon-inside left-icon"
                tabindex="-1"
              >
                <i data-feather="search"></i>
              </button>

              <input
                type="text"
                class="form-control"
                placeholder="Tìm kiếm theo tên"
                [value]="recipientSearch"
                (input)="
                  recipientSearch = $any($event.target).value || '';
                  page = 1
                "
              />
            </div>

            <!-- List -->
            <div>
              <ul class="list-unstyled mb-2">
                <li
                  *ngFor="let r of pagedRecipients; trackBy: trackById"
                  class="d-flex align-items-center justify-content-between py-1 border-bottom"
                >
                  <div class="d-flex align-items-center gap-8px">
                    <div class="rounded-circle d-inline-flex align-items-center justify-content-center width-28px height-28px">
                      <i class="width-16px height-16px" data-feather="user"></i>
                    </div>
                    <div>
                      <div class="fw-semibold">
                        {{ r.name || "User " + r.id }}
                      </div>
                      <small class="text-muted">
                        {{ r.organization_name || "" }}
                      </small>
                    </div>
                  </div>
                  <button 
                    *ngIf="![campaignStatus.STATUS_ONGOING, campaignStatus.STATUS_DONE].includes(status)"
                    type="button" 
                    class="btn btn-icon btn-flat-danger" 
                    ngbTooltip="Xóa"
                    container="body"
                    (click)="removeRecipient(r)"
                    rippleEffect>
                    <span [data-feather]="'x'"></span>
                  </button>
                </li>
              </ul>

              <div *ngIf="totalFiltered === 0" class="text-muted">
                Chưa có người nhận
              </div>

              <nav *ngIf="totalPages > 1" class="mt-2">
                <ul
                  class="pagination pagination-sm justify-content-center mb-0 custom-pager"
                >
                  <li class="page-item" [class.disabled]="page === 1">
                    <a
                      class="page-link"
                      href=""
                      (click)="$event.preventDefault(); goToPage(1)"
                      aria-label="First"
                      >&laquo;</a
                    >
                  </li>
                  <li class="page-item" [class.disabled]="page === 1">
                    <a
                      class="page-link"
                      href=""
                      (click)="$event.preventDefault(); prev()"
                      aria-label="Previous"
                      >&lsaquo;</a
                    >
                  </li>

                  <li
                    class="page-item"
                    *ngFor="let p of pageList"
                    [class.active]="p === page"
                  >
                    <a
                      class="page-link"
                      href=""
                      (click)="$event.preventDefault(); goToPage(p)"
                      >{{ p }}</a
                    >
                  </li>

                  <li
                    class="page-item"
                    [class.disabled]="page === totalPages"
                  >
                    <a
                      class="page-link"
                      href=""
                      (click)="$event.preventDefault(); next()"
                      aria-label="Next"
                      >&rsaquo;</a
                    >
                  </li>
                  <li
                    class="page-item"
                    [class.disabled]="page === totalPages"
                  >
                    <a
                      class="page-link"
                      href=""
                      (click)="
                        $event.preventDefault(); goToPage(totalPages)
                      "
                      aria-label="Last"
                      >&raquo;</a
                    >
                  </li>
                </ul>
              </nav>
            </div>
          </div>

          <div class="col-lg-8 col-12 pl-1">
            <div class="d-flex justify-content-between align-items-center mb-1">
              <h4 class="mb-0 fw-bold">
                Nội dung chiến dịch
              </h4>
              <div class="invisible">
                <button
                  type="button"
                  class="btn btn-sm btn-primary d-inline-flex align-items-center"
                >
                  <i data-feather="plus" class="me-1"></i>
                </button>
              </div>
            </div>

            <div class="row">
              <!-- Code -->
              <div class="col-3 mb-2">
                <label>Code<span class="text-danger pl-25">*</span></label>
                <div class="input-with-icon">
                  <input
                    type="text"
                    class="form-control"
                    formControlName="code"
                    (input)="onIdValueChange($event)"
                    placeholder="Nhập mã chiến dịch"
                    appFormControlValidation
                  />

                  <button
                    type="button"
                    class="btn btn-icon btn-flat-secondary btn-icon-inside right-icon"
                    aria-label="Copy ID"
                    tabindex="-1"
                    (click)="copyId()"
                    rippleEffect
                  >
                    <span data-feather="copy"></span>
                  </button>
                </div>
              </div>

              <!-- Tên chiến dịch -->
              <div class="col-9 mb-2">
                <label>Tên chiến dịch<span class="text-danger pl-25">*</span></label>
                <input
                  type="text"
                  class="form-control"
                  placeholder="Tên của chiến dịch"
                  formControlName="name"
                  [value]="nameValue"
                  appFormControlValidation
                />
              </div>
              <!-- Mô tả chiến dịch -->
              <div class="col-12 mb-2">
                <label>Mô tả chiến dịch<span class="text-danger pl-25">*</span></label>

                <fieldset class="form-group">
                  <textarea 
                    class="form-control" 
                    id="basicTextarea" 
                    rows="6" 
                    placeholder="Mô tả nội dung, mục đích campaign"
                    formControlName="description"
                    [value]="descriptionValue"
                    appFormControlValidation
                  ></textarea>
                </fieldset>
              </div>
              <!-- ID template -->
              <div class="col-12 mb-2">
                <label>Email<span class="text-danger pl-25">*</span></label>

                <ng-select
                  [items]="templateOptions"
                  bindLabel="label"
                  bindValue="id"
                  placeholder="Chọn template"
                  formControlName="email"
                  [searchable]="true"
                  [clearable]="false"
                  appFormControlValidation
                >
                </ng-select>
              </div>

              <!-- Thời gian gửi -->
              <div class="col-12 mb-2">
                <label>Thời gian gửi<span class="text-danger pl-25">*</span></label>
                <div class="input-with-icon">
                  <ng2-flatpickr
                    #sendTimeRef
                    [config]="basicDateOptions"
                    placeholder="Chọn thời gian"
                    formControlName="time"
                    appFormControlValidation
                  ></ng2-flatpickr>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="d-flex justify-content-end w-100">
              <button
                type="button"
                class="btn btn-secondary ml-1"
                (click)="onCancel()"
              >
                Hủy
              </button>
              <button
                *ngIf="type === 'add'"
                type="button"
                (click)="saveDraft()"
                class="btn btn-outline-primary ml-1"
              >
                Lưu nháp
              </button>
              <button
                *ngIf="type === 'add'"
                type="submit"
                class="btn btn-primary ml-1"
              >
                Chạy chiến dịch
              </button>
              <button
                *ngIf="
                  type === 'update' &&
                  ![campaignStatus.STATUS_ONGOING, 
                    campaignStatus.STATUS_DONE
                  ].includes(status)"
                type="button"
                (click)="updateCampaign()"
                class="btn btn-primary ml-1"
              >
                Lưu chiến dịch
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

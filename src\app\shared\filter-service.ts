import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class FilterStorageService {
  private STORAGE_KEY = 'filter';

  private loadAll(): Record<string, any> {
    return JSON.parse(localStorage.getItem(this.STORAGE_KEY) || '{}');
  }

  private saveAll(filters: Record<string, any>) {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filters));
  }

  /** Lưu filter cho module + key */
  save(module: string, key: string, value: any) {
    const allFilters = this.loadAll();
    allFilters[module] = allFilters[module] || {};
    allFilters[module][key] = value;
    this.saveAll(allFilters);
  }

  /** Lấy filter theo module + key */
  get(module: string, key: string): any {
    const allFilters = this.loadAll();
    return allFilters[module]?.[key] ?? null;
  }

  /** <PERSON><PERSON>y toàn bộ filter của module */
  getModule(module: string): Record<string, any> | null {
    const allFilters = this.loadAll();
    return allFilters[module] || null;
  }

  /** Xoá 1 key trong module */
  clear(module: string, key: string) {
    const allFilters = this.loadAll();
    if (allFilters[module]) {
      delete allFilters[module][key];
      this.saveAll(allFilters);
    }
  }

  /** Xoá toàn bộ module */
  clearModule(module: string) {
    const allFilters = this.loadAll();
    delete allFilters[module];
    this.saveAll(allFilters);
  }

  /** Xoá hết filter */
  clearAll() {
    localStorage.removeItem(this.STORAGE_KEY);
  }
}

// network-status.service.ts
import { Injectable, NgZone } from "@angular/core";
import { BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class NetworkStatusService {
  private onlineSubject = new BehaviorSubject<boolean>(navigator.onLine);
  public online$ = this.onlineSubject.asObservable();

  constructor(private ngZone: NgZone) {
    window.addEventListener("online", () => this.updateStatus(true));
    window.addEventListener("offline", () => this.updateStatus(false));
  }

  private updateStatus(isOnline: boolean) {
    this.ngZone.run(() => {
      this.onlineSubject.next(isOnline);
    });
  }
}

<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumb"></app-breadcrumb>
  <div class="card">
    <div class="card-header">
      <h4 class="card-title"><b>Nội dung Email</b></h4>
    </div>

    <div class="card-body">
      <form [formGroup]="form">
        <div class="row">
          <div class="col-12 mb-1">
            <div class="form-group">
              <label for="email-type">Loại Email<span class="text-danger">*</span></label>
              <ng-select
                id="email-type"
                [ngClass]="{ 'is-invalid': form.get('type')?.invalid && form.get('type')?.touched }"
                [clearable]="false"
                formControlName="type"
                placeholder="Lọc theo loại"
              >
                <!-- <ng-option value="transactional_email">Transactional Email</ng-option> -->
                <ng-option value="campaign_email">Campaign Email</ng-option>
              </ng-select>
              <div *ngIf="form.get('type')?.invalid && form.get('type')?.touched" class="invalid-feedback">
                Loại Email là bắt buộc
              </div>
            </div>
          </div>
          <div class="col-xl-3 col-md-3 col-12 mb-1">
            <div class="form-group">
              <label for="email-code">Code<span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="email-code"
                     [ngClass]="{ 'is-invalid': form.get('code')?.invalid && form.get('code')?.touched }"
                     formControlName="code" placeholder="Email ID" required />
              <div *ngIf="form.get('code')?.invalid && form.get('code')?.touched" class="invalid-feedback">
                Code là bắt buộc
              </div>
            </div>
          </div>
          <div class="col-xl-9 col-md-9 col-12 mb-1">
            <div class="form-group">
              <label for="email-title">Tiêu đề<span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="email-title" 
                     [ngClass]="{ 'is-invalid': form.get('title')?.invalid && form.get('title')?.touched }"
                     formControlName="title" placeholder="Nhập tiêu đề" required />
              <div *ngIf="form.get('title')?.invalid && form.get('title')?.touched" class="invalid-feedback">
                Tiêu đề là bắt buộc
              </div>
            </div>
          </div>
          <div class="col-12 mb-1">
            <div class="form-group">
              <label for="email-content">Nội dung<span class="text-danger">*</span></label>
              <quill-editor #EmailContent 
                            formControlName="content" 
                            placeholder="Nhập nội dung email tại đây..." 
                            [modules]="editorModules"
                            [ngClass]="{ 'is-invalid': form.get('content')?.invalid && form.get('content')?.touched }"
              >
                <div quill-editor-toolbar>
                  <span class="ql-formats">
                    <button class="ql-bold" ngbTooltip="In đậm"></button>
                    <button class="ql-italic" ngbTooltip="Nghiêng"></button>
                    <button class="ql-underline" ngbTooltip="Gạch chân"></button>
                    <button class="ql-strike" ngbTooltip="Gạch ngang"></button>
                  </span>

                  <span class="ql-formats">
                    <select class="ql-header">
                      <option selected>Thường</option>
                      <option value="1">H1</option>
                      <option value="2">H2</option>
                      <option value="3">H3</option>
                      <option value="4">H4</option>
                      <option value="5">H5</option>
                      <option value="6">H6</option>
                    </select>
                    <!-- <select class="ql-font">
                      <option selected></option>
                      <option value="arial">Arial</option>
                      <option value="times-new-roman">Times New Roman</option>
                      <option value="roboto">Roboto</option>
                      <option value="courier">Courier</option>
                      <option value="mirza">Mirza</option>
                    </select> -->
                    <select class="ql-size">
                      <option value="small">Nhỏ</option>
                      <option selected>Thường</option> <!-- mặc định normal -->
                      <option value="large">Lớn</option>
                      <option value="huge">Rất lớn</option>
                    </select>
                  </span>

                  <span class="ql-formats">
                    <select class="ql-color"></select>
                    <select class="ql-background"></select>
                  </span>

                  <span class="ql-formats">
                    <button class="ql-script" value="sub" ngbTooltip="Chỉ số dưới"></button>
                    <button class="ql-script" value="super" ngbTooltip="Chỉ số trên"></button>
                  </span>

                  <span class="ql-formats">
                    <select class="ql-align">
                      <option selected></option>
                      <option value="center" ngbTooltip="Căn giữa"></option>
                      <option value="right" ngbTooltip="Căn phải"></option>
                      <option value="justify" ngbTooltip="Căn trái"></option>
                    </select>
                  </span>

                  <span class="ql-formats">
                    <button class="ql-list" value="ordered" ngbTooltip="Đánh danh sách thứ tự"></button>
                    <button class="ql-list" value="bullet" ngbTooltip="Đánh danh sách đầu dòng"></button>
                    <button class="ql-indent" value="-1" ngbTooltip="Căn lề -1"></button>
                    <button class="ql-indent" value="+1" ngbTooltip="Căn lề +1"></button>
                  </span>

                  <span class="ql-formats">
                    <button class="ql-blockquote" ngbTooltip="Trích dẫn"></button>
                    <button class="ql-code-block" ngbTooltip="Code Block"></button>
                  </span>

                  <span class="ql-formats">
                    <button class="ql-link" ngbTooltip="Thêm Link"></button>
                    <button class="ql-image" ngbTooltip="Thêm Ảnh"></button>
                    <!-- <button class="ql-video" ngbTooltip="Thêm Video"></button> -->
                    <button class="ql-formula" ngbTooltip="Formula"></button>
                  </span>

                  <span class="ql-formats">
                    <button class="ql-clean" ngbTooltip="Xóa format"></button>
                  </span>
                </div>
              </quill-editor>
              <div *ngIf="form.get('content')?.invalid && form.get('content')?.touched" class="invalid-feedback d-block">
                Nội dung là bắt buộc
              </div>
            </div>
          </div>
          <!-- <div class="col-12 mb-1">
            <div class="form-group">
              <label for="email-footer">Nội dung chân trang<span class="text-danger">*</span></label>
              <input type="text" id="email-footer" class="form-control"
                     [ngClass]="{ 'is-invalid': form.get('title')?.invalid && form.get('title')?.touched }" 
                     formControlName="footer" placeholder="Nhập nội dung chân trang"
                     required />
              <div *ngIf="form.get('footer')?.invalid && form.get('footer')?.touched" class="invalid-feedback">
                Nội dung chân trang là bắt buộc
              </div>
            </div>
          </div> -->
        </div>

        <div class="d-flex justify-content-end">
          <button type="button" class="btn btn-secondary mr-1" (click)="cancel()" rippleEffect>Hủy</button>
          <!-- <button type="button" class="btn btn-flat-primary preview-btn mr-1" rippleEffect>Xem trước</button> -->
          <button type="button" class="btn btn-primary" (click)="submitForm()" rippleEffect>{{ id ? "Cập nhật" : "Thêm mẫu" }}</button>
        </div>
      </form>
    </div>
  </div>
</div>
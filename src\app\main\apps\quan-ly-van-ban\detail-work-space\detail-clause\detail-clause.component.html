<div class="detail-clause-container">
  <div class="detail-clause-header d-flex align-items-center p-1">
    <span class="cursor-pointer text-primary mr-1" (click)="goBack()"
      ><i data-feather="arrow-left-circle" size="24"></i
    ></span>
    <b class="m-0 one-line" [ngbTooltip]="clauseName" container="body">{{
      clauseName
    }}</b>
  </div>
  <div class="detail-clause-content">
    <ul
      ngbNav
      #nav="ngbNav"
      class="nav-tabs font-sm"
      (navChange)="changeNav($event)"
      [activeId]="avtiveTab"
    >
      <li ngbNavItem [ngbNavItem]="'lienquan'">
        <a ngbNavLink>Điều khoản liên quan ({{ totalRelateClause }})</a>
        <ng-template ngbNavContent>
          <div class="detail-clause-tab-content">
            <div class="header-clause">
              <div class="row align-items-center no-gutters">
                <!-- Input -->
                <div class="col-4 col-md-4 col-xl-3 col-xxl-3 pr-md-2 mb-1">
                  <div class="form-group m-0">
                    <input
                      type="text"
                      class="form-control"
                      id="basicInput"
                      placeholder="Tìm kiếm..."
                      [formControl]="textSearchRelateClause"
                    />
                  </div>
                </div>

                <!-- Select -->
                <div class="col-4 col-md-3 col-xl-2 col-xxl-2 mb-1">
                  <div class="form-group m-0">
                    <select
                      class="detail-clause-select-max-width form-control"
                      [(ngModel)]="conclusion"
                      (change)="filterByConclusion($event)"
                    >
                      <option value="">Tất cả</option>
                      <option value="Có mâu thuẫn">Có mâu thuẫn</option>
                      <option value="Không mâu thuẫn">Không mâu thuẫn</option>
                      <option value="Chưa so sánh">Chưa so sánh</option>
                    </select>
                  </div>
                </div>

                <div
                  class="col-12 col-md-5 col-xl-7 col-xxl-7 d-flex justify-content-end align-items-center mb-1"
                >
                  <div
                    class="mr-2 cursor-pointer show-button-toggle"
                    [ngbTooltip]="
                      isShowTable
                        ? 'Hiển thị dạng danh sách'
                        : 'Hiển thị dạng bảng'
                    "
                    container="body"
                    (click)="isShowTable = !isShowTable"
                  >
                    <img
                      [src]="
                        isShowTable
                          ? 'assets/images/icons/show-table-active.svg'
                          : 'assets/images/icons/show-table.svg'
                      "
                      alt="show-table"
                    />
                    <span class="show-button-label">
                      {{ isShowTable ? "Danh sách" : "Dạng bảng" }}
                    </span>
                  </div>
                  <div
                    *ngIf="totalRelateClause !== 0"
                    class="mr-2 cursor-pointer show-button-toggle"
                    ngbTooltip="Xuất kết quả so sánh"
                    container="body"
                    (click)="downloadReport()"
                  >
                    <img
                      src="
                      assets/images/icons/export.svg
                      "
                      alt="show-table"
                    />
                    <span class="detail-clause-padding-7 show-button-label"
                      >Xuất báo cáo</span
                    >
                  </div>

                  <button
                    type="button"
                    *ngIf="totalRelateClause !== 0"
                    class="btn round btn-outline-secondary mr-2 position-relative d-flex align-items-center justify-content-center"
                    rippleEffect
                    (click)="openConfirmModal()"
                    [disabled]="isBatchLoading"
                  >
                    So sánh tất cả
                    <span
                      *ngIf="isBatchLoading"
                      class="spinner-border spinner-border-sm text-primary position-absolute"
                      role="status"
                    >
                      <span class="sr-only">Loading...</span>
                    </span>
                  </button>

                  <button
                    type="button"
                    [ngClass]="
                      isToggleAddClause
                        ? 'btn btn-primary round'
                        : 'btn btn-outline-secondary round'
                    "
                    rippleEffect
                    (click)="addRelateClause()"
                  >
                    Bổ sung
                  </button>
                </div>
              </div>
            </div>
            <div
              *ngIf="isShowTable"
              class="clause-content clause-content-table"
            >
              <ngx-datatable
                #tableRowDetails
                [rows]="listDieuKhoanLienQuanFilter"
                [rowHeight]="58"
                class="bootstrap core-bootstrap cursor"
                [columnMode]="ColumnMode.force"
                [headerHeight]="40"
                [footerHeight]="50"
                [scrollbarH]="true"
                [limit]="limit"
                (activate)="onActivate($event)"
                [externalPaging]="true"
                (page)="setPage($event)"
                [offset]="page - 1"
                [count]="totalItem"
              >
                <ngx-datatable-row-detail [rowHeight]="'100%'">
                  <ng-template
                    let-row="row"
                    let-expanded="expanded"
                    ngx-datatable-row-detail-template
                  >
                    <div>
                      <div class="detail-clause-text-wrap-pretty ml-75 pl-5 pt-75">
                        <div class="decree-description wrap-text">
                          <p
                            appExpandableText
                            *ngIf="row.content != ''"
                            [text]="row?.content"
                          ></p>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </ngx-datatable-row-detail>
                <ngx-datatable-column
                  [width]="50"
                  [resizeable]="false"
                  [sortable]="false"
                  [draggable]="false"
                  [canAutoResize]="false"
                >
                  <ng-template
                    let-row="row"
                    let-expanded="expanded"
                    ngx-datatable-cell-template
                  >
                    <a
                      href="javascript:void(0)"
                      [class.datatable-icon-right]="!expanded"
                      [class.datatable-icon-down]="expanded"
                      title="Expand/Collapse Row"
                      (click)="rowDetailsToggleExpand(row)"
                    >
                    </a>
                  </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column
                  name="Tên văn bản"
                  prop="document_data.title"
                  [width]="250"
                >
                </ngx-datatable-column>

                <ngx-datatable-column
                  name="Vị trí"
                  prop="position"
                  [width]="150"
                >
                </ngx-datatable-column>
                <ngx-datatable-column
                  name="Kết quả"
                  prop="reason"
                  [width]="150"
                >
                <ng-template
                  let-result="value"
                  ngx-datatable-cell-template
                >
                  <div
                    *ngIf="result"
                    [ngClass]="[
                      'badge',
                      result === 'Không mâu thuẫn'
                        ? 'badge-light-success'
                        : 'badge-light-danger'
                    ]"
                  >
                    {{ result }}
                  </div>
                </ng-template>
                </ngx-datatable-column>

                <ngx-datatable-column
                  name="Ngày ban hành"
                  prop="document_data.ngay_ban_hanh"
                  [width]="150"
                >
                  <ng-template
                    let-status="value"
                    let-row="row"
                    ngx-datatable-cell-template
                  >
                    {{
                      row.document_data.ngay_ban_hanh
                        | date : "dd-MM-yyyy" : "+0000"
                    }}
                  </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column
                  name="Ngày có hiệu lực"
                  prop="document_data.ngay_co_hieu_luc"
                  [width]="190"
                >
                  <ng-template
                    let-value="value"
                    let-row="row"
                    ngx-datatable-cell-template
                  >
                    {{
                      row.document_data.ngay_co_hieu_luc
                        | date : "dd-MM-yyyy" : "+0000"
                    }}
                  </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column
                  name="Trạng thái hiệu lực"
                  prop="document_data.tinh_trang_hieu_luc"
                  [width]="210"
                >
                  <ng-template
                    let-status="value"
                    let-row="row"
                    ngx-datatable-cell-template
                  >
                    <div
                      [ngClass]="[
                        'badge',
                        status === 'Còn hiệu lực'
                          ? 'badge-light-primary'
                          : status === 'Hết hiệu lực một phần'
                          ? 'badge-light-warning'
                          : status === 'Hết hiệu lực toàn bộ'
                          ? 'badge-light-danger'
                          : 'badge-light-info'
                      ]"
                    >
                      {{ status }}
                    </div>
                  </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column
                  name="Hành động"
                  [width]="250"
                  [sortable]="false"
                >
                  <ng-template ngx-datatable-cell-template let-row="row">
                    <span ngbDropdown container="body">
                      <a
                        ngbDropdownToggle
                        class="hide-arrow text-primary mr-50"
                        data-toggle="dropdown"
                        aria-haspopup="true"
                        aria-expanded="false"
                        (click)="compareClause(row)"
                      >
                        So sánh
                      </a>
                      <a
                        ngbDropdownToggle
                        class="hide-arrow text-primary mr-50"
                        data-toggle="dropdown"
                        aria-haspopup="true"
                        aria-expanded="false"
                        (click)="deleteClause(row)"
                      >
                        Xoá
                      </a>
                      <a
                        ngbDropdownToggle
                        class="hide-arrow text-primary mr-50"
                        data-toggle="dropdown"
                        aria-haspopup="true"
                        aria-expanded="false"
                        appcopy
                        [text]="row.document_data.title"
                      >
                        Sao chép
                      </a>
                    </span>
                  </ng-template>
                </ngx-datatable-column>
                <ngx-datatable-column
                  name="Chi tiết so sánh"
                  [width]="170"
                  [sortable]="false"
                >
                  <ng-template ngx-datatable-cell-template let-row="row">
                    <span ngbDropdown container="body">
                      <a
                        *ngIf="row.reason"
                        ngbDropdownToggle
                        class="hide-arrow text-primary"
                        data-toggle="dropdown"
                        aria-haspopup="true"
                        aria-expanded="false"
                        (click)="showModalDetailCompare(row)"
                      >
                        Chi tiết so sánh
                      </a>
                    </span>
                  </ng-template>
                </ngx-datatable-column>
              </ngx-datatable>
            </div>
            <div
              class="clause-content clause-content-list"
              *ngIf="!isShowTable"
            >
              <ng-container
                *ngFor="let item of listDieuKhoanLienQuanFilter; let i = index"
              >
                <div
                  class="d-flex clause-related justify-content-between cursor-pointer"
                  (click)="viewFileDieuKhoanLienQuan(item)"
                >
                  <div class="w-100">
                    <div class="decree-card pl-0">
                      <div class="decree-header">
                        <div class="detail-clause-display-flow font-weight-bolder">
                          <span *ngIf="i + (page - 1) * 12 + 1 < 9">0</span
                          >{{ i + (page - 1) * 12 + 1 }}.
                          {{ item.document_data.title }}
                          <img
                            (click)="
                              deleteClause(item); $event.stopPropagation()
                            "
                            class="mr-1 cursor-pointer"
                            src="assets/images/icons/delete-clause.svg"
                            alt="delete-clause"
                            ngbTooltip="Xoá"
                            container="body"
                          />
                          <span
                            (click)="
                              $event.stopPropagation();
                              copyText(item.document_data.title)
                            "
                            class="text-primary cursor-pointer mr-2"
                            ngbTooltip="Sao chép"
                            container="body"
                            ><i data-feather="copy"></i
                          ></span>
                          <div class="detail-clause-inline-block">
                            <div
                              class="badge badge-pill"
                              [ngClass]="{
                                'badge-success':
                                  item.reason === 'Không mâu thuẫn',
                                'badge-danger':
                                  item.reason !== 'Không mâu thuẫn'
                              }"
                              *ngIf="item.reason"
                              (click)="
                                viewDetailCompare(item);
                                $event.stopPropagation()
                              "
                            >
                              {{ item.reason }}
                            </div>
                            <span
                              class="text-primary cursor-pointer m-0 ml-1"
                              *ngIf="item.reason"
                              (click)="
                                showModalDetailCompare(item);
                                $event.stopPropagation()
                              "
                            >
                              Chi tiết so sánh
                            </span>
                          </div>
                        </div>
                      </div>
                      <div class="decree-position one-line">
                        <strong
                          >Vị trí: {{ item.position }} -
                          {{ item.title }}</strong
                        >
                      </div>
                      <div class="decree-description wrap-text">
                        <p
                          appExpandableText
                          *ngIf="item.content != '' || item?.raw_content"
                          [text]="
                            item?.raw_content
                              ? item?.raw_content
                              : item?.content
                          "
                        ></p>
                      </div>

                      <div class="decree-info">
                        <span
                          >Hiệu lực:
                          <span
                            [ngClass]="{
                              'text-danger':
                                item.document_data.tinh_trang_hieu_luc ===
                                'Hết hiệu lực toàn bộ',
                              'text-success':
                                item.document_data.tinh_trang_hieu_luc ===
                                'Còn hiệu lực',
                              'text-warning':
                                item.document_data.tinh_trang_hieu_luc ===
                                'Hết hiệu lực một phần',
                              'text-primary':
                                item.document_data.tinh_trang_hieu_luc ===
                                'Chưa có hiệu lực',
                              'text-secondary':
                                item.document_data.tinh_trang_hieu_luc ===
                                'Ngưng hiệu lực một phần',
                              'text-muted':
                                item.document_data.tinh_trang_hieu_luc ===
                                'Không còn phù hợp'
                            }"
                            >{{ item.document_data.tinh_trang_hieu_luc }}</span
                          ></span
                        >
                        <span
                          >Ban hành:
                          <span>
                            {{
                              item.document_data.ngay_ban_hanh
                                | date : "dd-MM-yyyy"
                            }}
                          </span></span
                        >
                        <span
                          >Áp dụng:
                          <span>
                            {{
                              item.document_data.ngay_co_hieu_luc
                                | date : "dd-MM-yyyy"
                            }}
                          </span></span
                        >
                      </div>
                    </div>
                  </div>
                  <div
                    class="detail-clause-min-width-55 text-primary cursor-pointer d-flex align-items-center"
                    (click)="
                      !isBatchLoading && compareClause(item);
                      $event.stopPropagation()
                    "
                    [class.disabled]="isBatchLoading"
                    [style.cursor]="isBatchLoading ? 'not-allowed' : 'pointer'"
                  >
                    <p *ngIf="!item.isLoading">So sánh</p>
                    <span
                      *ngIf="item.isLoading"
                      class="spinner-border spinner-border-sm text-primary"
                      role="status"
                    >
                      <span class="sr-only">Loading...</span>
                    </span>
                  </div>
                </div>
              </ng-container>

              <ng-container *ngIf="listDieuKhoanLienQuanFilter?.length == 0">
                <div class="no-file-container">
                  <img src="assets/images/icons/no-file.svg" alt="no-file" />
                  <p class="font-weight-bolder h3 mt-2">
                    Không tìm thấy điều khoản liên quan
                  </p>
                </div>
              </ng-container>
            </div>
            <span
              class="d-flex w-100 justify-content-center"
              *ngIf="totalRelateClause > 12 && !isShowTable"
            >
              <ngb-pagination
                [maxSize]="3"
                pageSize="12"
                [collectionSize]="totalRelateClause"
                [(page)]="page"
                aria-label="Default pagination"
                [rotate]="true"
                [ellipses]="false"
                [boundaryLinks]="true"
                (pageChange)="onPageChange($event)"
              >
              </ngb-pagination>
            </span>
          </div>
        </ng-template>
      </li>
      <li ngbNavItem [ngbNavItem]="'noidung'">
        <a ngbNavLink
          >Thẩm quyền nội dung ({{ listThamQuyenNoiDung.length }})</a
        >
        <ng-template ngbNavContent>
          <div
            class="detail-clause-tab-content"
            *ngIf="listThamQuyenNoiDung?.length != 0"
          >
            <div
              class="clause-content"
              *ngIf="listThamQuyenNoiDung?.length > 0"
            >
              <div
                class="col-12 mb-1 d-flex justify-content-end pr-0 d-flex align-items-center"
              >
                <div
                  class="mr-1 cursor-pointer"
                  ngbTooltip="Chú thích"
                  container="body"
                  placement="left"
                  [ngbPopover]="popoverContent"
                >
                  <img
                    src="assets/images/icons/question-circel.svg"
                    alt="show-table"
                  />
                </div>
              </div>
              <ng-container
                *ngFor="let item of listThamQuyenNoiDung; let i = index"
              >
                <hr />
                <span class="collapse-icon mt-1" id="detail-clause-content">
                  <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
                    <ngb-panel>
                      <ng-template ngbPanelTitle>
                        <span
                          class="detail-clause-width-95 lead collapse-title card-title d-flex flex-column"
                        >
                          <div>
                            <button
                              [ngClass]="{
                                'badge-danger': item.ket_qua == 'Mã đỏ',
                                'badge-success': item.ket_qua == 'Mã xanh',
                                'badge-warning': item.ket_qua == 'Mã vàng',
                                'badge-info': item.ket_qua == 'Mã xanh dương'
                              }"
                              class="detail-clause-badge-button btn btn-sm round"
                              type="button"
                              rippleEffect
                            >
                              {{ item.ket_qua }}
                            </button>
                          </div>
                          <span>
                            <p class="m-0">
                              <span class="font-weight-bolder">
                                Vấn đề ban hành:
                              </span>
                              &nbsp;
                              <span [innerHTML]="item.van_de_ban_hanh"></span>
                            </p>
                            <p class="m-0">
                              <span class="font-weight-bolder"> Lý do: </span>
                              &nbsp;
                              <span [innerHTML]="item.ly_do"></span>
                            </p>
                            <p class="m-0">
                              <span class="font-weight-bolder">
                                Cơ quan có thẩm quyền:
                              </span>
                              &nbsp;{{ item.co_quan_co_tham_quyen }}
                            </p>
                          </span>
                        </span>
                      </ng-template>
                      <ng-template ngbPanelContent>
                        <b
                          class="text-primary"
                          *ngIf="item.quy_dinh_phap_luat_lien_quan?.length > 0"
                        >
                          Các quy định pháp luật có thông tin phân quyền có liên
                          quan:
                        </b>
                        <ng-container
                          *ngFor="
                            let clause of item.quy_dinh_phap_luat_lien_quan;
                            let i = index
                          "
                        >
                          <div
                            class="d-flex align-items-center py-1 folder-item"
                            (click)="viewFileDieuKhoanLienQuan(clause)"
                          >
                            <div class="detail-clause-align-self-start">
                              <span class="d-flex align-items-center">
                                <b class="m-0">
                                  <span *ngIf="i < 9">0</span>{{ i + 1 }}.
                                </b>
                              </span>
                            </div>
                            <div class="detail-clause-width-99">
                              <div class="wrap-text">
                                <span
                                  placement="bottom"
                                  [openDelay]="300"
                                  [ngbTooltip]="clause.title"
                                  class="font-weight-bolder text-truncate d-flex"
                                >
                                  <span class="truncate">
                                    {{ clause.title }}
                                    &nbsp;
                                  </span>
                                  <span
                                    (click)="
                                      $event.stopPropagation();
                                      copyText(claus.title)
                                    "
                                    ngbTooltip="Sao chép"
                                    container="body"
                                    class="text-primary cursor-pointer mr-2"
                                    ><i data-feather="copy"></i
                                  ></span>
                                </span>
                                <p class="m-0 font-weight-bolder">
                                  {{ clause.position }}
                                </p>
                                <p class="m-0">
                                  <span class="font-weight-bolder">
                                    Cơ quan có thẩm quyền:
                                  </span>
                                  &nbsp;{{ item.co_quan_co_tham_quyen }}
                                </p>

                                <i
                                  appExpandableText
                                  [text]="clause?.content"
                                  *ngIf="clause.content != ''"
                                >
                                </i>
                                <!-- để phân tách ra cho đều khoảng cách nếu không có nội dung -->
                                <div
                                  *ngIf="clause.content == ''"
                                  class="detail-clause-empty-spacer-41"
                                ></div>
                                <div class="d-flex mt-1">
                                  <p
                                    *ngIf="clause.tinh_trang_hieu_luc"
                                    class="m-0"
                                  >
                                    Hiệu lực:
                                    <span
                                      class="font-weight-bolder"
                                      [ngClass]="{
                                        'text-danger':
                                          clause.tinh_trang_hieu_luc ===
                                          'Hết hiệu lực toàn bộ',
                                        'text-success':
                                          clause.tinh_trang_hieu_luc ===
                                          'Còn hiệu lực',
                                        'text-warning':
                                          clause.tinh_trang_hieu_luc ===
                                          'Hết hiệu lực một phần',
                                        'text-primary':
                                          clause.tinh_trang_hieu_luc ===
                                          'Chưa có hiệu lực',
                                        'text-secondary':
                                          clause.tinh_trang_hieu_luc ===
                                          'Ngưng hiệu lực một phần',
                                        'text-muted':
                                          clause.tinh_trang_hieu_luc ===
                                          'Không còn phù hợp'
                                      }"
                                    >
                                      {{ clause.tinh_trang_hieu_luc }}
                                    </span>
                                  </p>
                                  <p *ngIf="clause.ngay_ban_hanh" class="m-0">
                                    Ban hành:
                                    <span>
                                      {{
                                        clause.ngay_ban_hanh
                                          | date : "dd-MM-yyyy"
                                      }}
                                    </span>
                                  </p>
                                  <p
                                    *ngIf="clause.ngay_co_hieu_luc"
                                    class="m-0"
                                  >
                                    Áp dụng:
                                    <span>
                                      {{
                                        clause.ngay_co_hieu_luc
                                          | date : "dd-MM-yyyy"
                                      }}
                                    </span>
                                  </p>
                                </div>
                                <hr />
                              </div>
                            </div>
                          </div>
                        </ng-container>
                      </ng-template>
                    </ngb-panel>
                  </ngb-accordion>
                </span>
                <hr />
              </ng-container>
            </div>
          </div>
          <ng-container *ngIf="listThamQuyenNoiDung?.length == 0">
            <div class="no-file-container">
              <img src="assets/images/icons/no-file.svg" alt="no-file" />
              <p class="font-weight-bolder h3 mt-2">
                Không phát hiện mâu thuẫn thẩm quyền nội dung
              </p>
            </div>
          </ng-container>
        </ng-template>
      </li>
      <p
        (click)="showContentClause()"
        class="text-primary mb-0 ml-auto align-self-center cursor-pointer"
      >
        Nội dung điều khoản
      </p>
    </ul>
    <div [ngbNavOutlet]="nav" class="mt-2 detail-clause-nav-outlet"></div>
  </div>
</div>

<ng-template #detailCompareModal let-modal>
  <div class="modal-body">
    <div class="row">
      <div class="col-12">
        <div class="form-group">
          <label
            for="basicTextarea"
            class="w-100 align-items-center d-flex justify-content-between"
            >Chi tiết so sánh
            <div class="">
              <button
                class="btn btn-sm ml-auto p-0"
                (click)="modal.dismiss('Cross click')"
              >
                <img src="assets/images/icons/x.svg" alt="x" />
              </button></div
          ></label>
        </div>
      </div>
      <div class="col-12">
        <div class="header-compare">
          <div ngbDropdown>
            <div
              ngbDropdownToggle
              class="badge badge-pill mb-1 cursor-pointer"
              [ngClass]="{
                'badge-success': clauseCompare.ket_luan === 'Không mâu thuẫn',
                'badge-danger': clauseCompare.ket_luan !== 'Không mâu thuẫn'
              }"
            >
              {{ clauseCompare.ket_luan }}
            </div>
            <div ngbDropdownMenu aria-labelledby="dropdownMenuButton">
              <a
                ngbDropdownItem
                *ngIf="clauseCompare.ket_luan != 'Không mâu thuẫn'"
                (click)="changeStatusClasue('Không mâu thuẫn')"
                >Không mâu thuẫn</a
              >
              <a
                ngbDropdownItem
                *ngIf="clauseCompare.ket_luan == 'Không mâu thuẫn'"
                (click)="changeStatusClasue('Phát hiện mâu thuẫn')"
                >Phát hiện mâu thuẫn</a
              >
            </div>
          </div>
          <div>
            <b>Tiến trình tư duy: </b>
            <span [contentEditable]="isEditClause" #tuduy>
              <p
                #editable
                [attr.data-index]="i"
                [attr.data-type]="'thinking'"
                class="thinking"
                [innerHTML]="clauseCompare.tien_trinh_tu_duy | markdown"
              ></p
            ></span>
          </div>
          <div>
            <b>Diễn giải: </b>
            <span [contentEditable]="isEditClause" #diendai>
              <p
                #editable
                [attr.data-index]="i"
                [attr.data-type]="'reason'"
                class="reason"
                [innerHTML]="clauseCompare.ly_do | markdown"
              ></p
            ></span>
          </div>
          <div>
            <b>Giải pháp đề xuất: </b>
            <span [contentEditable]="isEditClause" #giaiphap>
              <p
                #editable
                [attr.data-index]="i"
                [attr.data-type]="'solution'"
                class="solution"
                [innerHTML]="clauseCompare.giai_phap | markdown"
              ></p
            ></span>
          </div>
          <div class="cursor-pointer">
            <span ngbTooltip="Chỉnh sửa" (click)="toggleEdit()">
              <i data-feather="edit"></i>
            </span>
            <span
              (click)="saveClause(diendai, giaiphap, tuduy)"
              ngbTooltip="Lưu"
              class="ml-50 text-primary"
            >
              <i data-feather="check-circle"></i>
            </span>
          </div>
          <hr />
        </div>
        <div class="content-compare mt-1">
          <div class="row">
            <div class="col-6 left content-compare-left">
              <b>{{ fileInfor.name || fileInfor.fileName }}</b>
              <br />
              <b class="content-compare-title"
                >{{ clauseCompared.position }} - {{ clauseCompared.title }}</b
              >
              <div class="decree-info">
                <span
                  >Hiệu lực:
                  <span
                    [ngClass]="{
                      'text-danger':
                        fileInfor.tinh_trang_hieu_luc ===
                        'Hết hiệu lực toàn bộ',
                      'text-success':
                        fileInfor.tinh_trang_hieu_luc === 'Còn hiệu lực',
                      'text-warning':
                        fileInfor.tinh_trang_hieu_luc ===
                        'Hết hiệu lực một phần',
                      'text-primary':
                        fileInfor.tinh_trang_hieu_luc === 'Chưa có hiệu lực',
                      'text-secondary':
                        fileInfor.tinh_trang_hieu_luc ===
                        'Ngưng hiệu lực một phần',
                      'text-muted':
                        fileInfor.tinh_trang_hieu_luc === 'Không còn phù hợp'
                    }"
                    >{{ fileInfor.tinh_trang_hieu_luc }}</span
                  ></span
                >
                <span
                  >Ban hành:
                  <span>
                    {{ fileInfor.ngay_ban_hanh | date : "dd-MM-yyyy" }}
                  </span></span
                >
                <span
                  >Áp dụng:
                  <span>
                    {{ fileInfor.ngay_co_hieu_luc | date : "dd-MM-yyyy" }}
                  </span></span
                >
              </div>
              <p class="wrap-text content-clause-compare">
                {{ clauseCompared.content }}
              </p>
            </div>
            <div class="col-6 right content-compare-right">
              <b>{{ clauseCompare.document_data.title }}</b>
              <br />
              <b class="content-compare-title">
                {{ clauseCompare.position }} - {{ clauseCompare.title }}</b
              >
              <div class="decree-info">
                <span
                  >Hiệu lực:
                  <span
                    [ngClass]="{
                      'text-danger':
                        clauseCompare.document_data.tinh_trang_hieu_luc ===
                        'Hết hiệu lực toàn bộ',
                      'text-success':
                        clauseCompare.document_data.tinh_trang_hieu_luc ===
                        'Còn hiệu lực',
                      'text-warning':
                        clauseCompare.document_data.tinh_trang_hieu_luc ===
                        'Hết hiệu lực một phần',
                      'text-primary':
                        clauseCompare.document_data.tinh_trang_hieu_luc ===
                        'Chưa có hiệu lực',
                      'text-secondary':
                        clauseCompare.document_data.tinh_trang_hieu_luc ===
                        'Ngưng hiệu lực một phần',
                      'text-muted':
                        clauseCompare.document_data.tinh_trang_hieu_luc ===
                        'Không còn phù hợp'
                    }"
                    >{{ clauseCompare.document_data.tinh_trang_hieu_luc }}</span
                  ></span
                >
                <span
                  >Ban hành:
                  <span>
                    {{
                      clauseCompare.document_data.ngay_ban_hanh
                        | date : "dd-MM-yyyy"
                    }}
                  </span></span
                >
                <span
                  >Áp dụng:
                  <span>
                    {{
                      clauseCompare.document_data.ngay_co_hieu_luc
                        | date : "dd-MM-yyyy"
                    }}
                  </span></span
                >
              </div>
              <p class="wrap-text content-clause-compare">
                {{ clauseCompare.content }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #contentClauseModal let-modal>
  <div class="modal-body">
    <div class="row">
      <div class="col-12">
        <div class="form-group">
          <label
            for="basicTextarea"
            class="w-100 align-items-center d-flex justify-content-between"
            >Nội dung điều khoản
            <div class="">
              <button
                class="btn btn-sm ml-auto p-0"
                (click)="modal.dismiss('Cross click')"
              >
                <img src="assets/images/icons/x.svg" alt="x" />
              </button></div
          ></label>
        </div>
      </div>
      <div class="col-12">
        <div class="clauseName mb-1 wrap-text">
          <b>Điều khoản: </b> {{ clause.position }} -
          <span
            appExpandableText
            maxWords="30"
            [text]="clauseName"
            [isShowFull]="true"
          >
          </span>
          <br />
          <b>Nội dung: </b>
          <span
            appExpandableText
            maxWords="50"
            [text]="clause.raw_content ? clause.raw_content : clause.content"
            [isShowFull]="true"
          >
          </span>
        </div>
      </div>
    </div>
  </div>
</ng-template>
<ng-template #popoverContent>
  <div class="text-dark p-1">
    <p>
      <span class="fw-bold text-success">Mã Xanh:</span> Vấn đề ban hành được
      giao đúng thẩm quyền đối với chủ thể ban hành văn bản cần rà soát trong
      các văn bản là căn cứ pháp lý của văn bản cần rà soát
    </p>

    <p>
      <span class="fw-bold text-primary">Mã Xanh dương:</span> Vấn đề ban hành
      được giao đúng thẩm quyền đối với chủ thể ban hành văn bản cần rà soát
      trong các văn bản không thuộc căn cứ pháp lý của văn bản cần rà soát
    </p>

    <p>
      <span class="fw-bold text-danger">Mã Đỏ:</span> Vấn đề ban hành không
      thuộc thẩm quyền đối với chủ thể ban hành văn bản cần rà soát trong các
      văn bản là căn cứ pháp lý của văn bản cần rà soát
    </p>

    <p>
      <span class="fw-bold text-warning">Mã Vàng:</span> Hệ thống không xác định
      được thẩm quyền của chủ thể ban hành văn bản cần rà soát đối với vấn đề
      ban hành được nhắc tới
    </p>
  </div>
</ng-template>

import { Component, OnInit, ViewChild, ViewEncapsulation } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbPopover } from "@ng-bootstrap/ng-bootstrap";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { AuthenticationService } from "app/auth/service";
import { FormType } from "app/models/FormType";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { debounceTime, takeUntil } from "rxjs/operators";
import Swal from "sweetalert2";
import { OrganizationService } from "../organization/organization.service";
import { UserService } from "./user.service";
import { FilterStorageService } from "app/shared/filter-service";


@Component({
  selector: "app-user",
  templateUrl: "./user.component.html",
  styleUrls: ["./user.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class UserComponent implements OnInit {
  @ViewChild("popover") popover: NgbPopover;

  public listUser: [] = [];
  public ColumnMode = ColumnMode;
  public limitTable: number = 12;
  public totalItem: number = 0;
  public breadcrumbDefault: object;
  public listStatusUser = [
    { value: "False", label: "Đã khoá" },
    { value: "True", label: "Hoạt động" },
  ];
  public page = 1;
  title: string;
  type: FormType;
  data: any;
  public listOrgan: any = [];
  public formGetUsers: FormGroup;
  public _unSubAll: Subject<any> = new Subject();
  public listPosition = [];
  public role: string;
  public formResetPassword: FormGroup;
  public submitted: boolean = false;
  public mergedPwdShow = false;
  public idUser: string;
  isPopoverEnabled = false;
  constructor(
    private _userService: UserService,
    private _filterStorage: FilterStorageService,
    private router: Router,
    private _organService: OrganizationService,
    private toast: ToastrService,
    private fb: FormBuilder,
    private _authenService: AuthenticationService
  ) {
    this.formGetUsers = this.fb.group({
      search: [this._filterStorage.get('user', 'search') ?? ""],
      organization_id: [this._filterStorage.get('user', 'organization_id') ?? null],
      active: [this._filterStorage.get('user', 'active') ?? null],
      page: [this._filterStorage.get('user', 'page') ?? 1],
    });

    this.role = _authenService.currentUserValue.role;
    if (this.role == "ADMIN") {
      this.formGetUsers.patchValue({
        organization_id: localStorage.getItem("organization_id"),
      });
    }
  }

  ngOnInit(): void {
    this.breadcrumbDefault = {
      links: [
        {
          name: "Quản lý người dùng",
          isHeader: true,
        },
      ],
    };
    Object.keys(this.formGetUsers.controls).forEach((controlName) => {
      this.formGetUsers
        .get(controlName)
        ?.valueChanges.pipe(takeUntil(this._unSubAll), debounceTime(300))
        .subscribe((value) => {
          if (controlName != "page") {
            this.formGetUsers.get("page").setValue(1, { emitEvent: false });
            this.page = 1;
          }
          this._filterStorage.save("user", controlName, value ?? null);
          this.getAllUser();
        });
    });
    this.formResetPassword = this.fb.group({
      password: [
        null,
        [
          Validators.minLength(6),
          Validators.pattern(
            /^(?=.*[A-Z])(?=.*[!@#$%^&*()_+{}\[\]:;<>,.?~\\/-]).{6,}$/
          ),
        ],
      ],
    });

    this.getAllOrgan();
    this.getAllUser();
  }
  getAllOrgan() {
    let params;
    if (this.role == "ADMIN") {
      params = {
        root_organization_id: localStorage.getItem("organization_id"),
      };
    } else {
      params = {};
    }
    this._organService.get(params).subscribe((res) => {
      if (res.results)
        this.listOrgan = this.buildFlatTreeForNgSelect(res.results);
      else this.listOrgan = this.buildFlatTreeForNgSelect(res);
    });
  }

  getAllUser() {
    const rawFormValue = this.formGetUsers.value;

    const body = Object.keys(rawFormValue).reduce((acc, key) => {
      acc[key] = rawFormValue[key] === null ? "" : rawFormValue[key];
      return acc;
    }, {} as { [key: string]: any });
    this._userService.get(body).subscribe((res) => {
      this.listUser = res.results;
      this.totalItem = res.count;
    });
  }
  addPosition() {
    this.router.navigate(["super-admin/user/user-control"], {
      queryParams: {
        type: "add",
      },
      queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
    });
  }
  editOrganization(data) {
    this._userService.data.next(data);
    this.router.navigate(["super-admin/user/user-control"], {
      queryParams: {
        type: "update",
      },
      queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
    });
  }
  onActivate(event) {
    if (event.type == "click" && event.column.name != "Hành động") {
      // console.log(event.row);

      this._userService.data.next(event.row);
      this.router.navigate(["super-admin/user/user-control"], {
        queryParams: {
          type: "update",
        },
        queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
      });
    }
    if (event.type == "click") {
      this.idUser = event.row.id;
    }
  }
  setPage({ offset }) {
    this.page = offset + 1;
    this.formGetUsers.patchValue({ page: offset + 1 });
    // this.getAllUser();
  }
  deleteUser(data) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this._userService.destroy(data.id).subscribe(
          (res) => {
            this.toast.success("Đã xoá người dùng", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
            this.getAllUser();
          },
          (error) => {
            // this.toast.error("Xoá người dùng", "Thất bại", {
            //   closeButton: true,
            //   positionClass: "toast-top-right",
            //   toastClass: "toast ngx-toastr",
            // });
          }
        );
      }
    });
  }
  buildFlatTreeForNgSelect(data: any[]): [] {
    // Bước 1: Tạo map id → node
    const nodeMap: { [id: string]: any } = {};
    const roots: any[] = [];

    data.forEach((org) => {
      nodeMap[org.id] = { id: org.id, name: org.name, children: [] };
    });

    // Bước 2: Gắn cha – con
    data.forEach((org) => {
      const node = nodeMap[org.id];
      if (org.parent_organization && nodeMap[org.parent_organization]) {
        nodeMap[org.parent_organization].children!.push(node);
      } else {
        roots.push(node);
      }
    });

    // Bước 3: Duyệt cây và tạo danh sách phẳng với indent
    const result: any = [];

    function traverse(node: any, level: number) {
      result.push({
        id: node.id,
        name: `${"— ".repeat(level)}${node.name}`,
      });
      node.children?.forEach((child) => traverse(child, level + 1));
    }

    roots.forEach((root) => traverse(root, 0));
    return result;
  }
  resetPassword(row) {
    // Swal.fire({
    //   title: "Bạn có chắc chắn muốn cấp lại mật khẩu?",
    //   icon: "warning",
    //   reverseButtons: true,
    //   showCancelButton: true,
    //   confirmButtonColor: "#008fd3",
    //   cancelButtonColor: "#EEE",
    //   confirmButtonText: "Đồng ý",
    //   cancelButtonText: "Hủy",
    //   customClass: {
    //     cancelButton: "swal-cancel-info",
    //     confirmButton: "swal-confirm-danger",
    //   },
    // }).then((result) => {
    //   if (result.isConfirmed) {
    //     this._userService.resetPassword(row.id).subscribe(
    //       (res) => {
    //         this.toast.success("Cấp lại mật khẩu", "Thành công", {
    //           closeButton: true,
    //           positionClass: "toast-top-right",
    //           toastClass: "toast ngx-toastr",
    //         });
    //         // this.getAllUser();
    //       },
    //       (error) => {
    //         this.toast.error(error.error, "Thất bại", {
    //           closeButton: true,
    //           positionClass: "toast-top-right",
    //           toastClass: "toast ngx-toastr",
    //         });
    //       }
    //     );
    //   }
    // });
  }

  updateStatus(row, status) {
    Swal.fire({
      title:
        status == "True"
          ? "Bạn có chắc chắn muốn kích hoạt người dùng?"
          : "Bạn có chắc chắn muốn vô hiệu hoá người dùng",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Đồng ý",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        const body = { status: status };
        this._userService.updateStatus(row.id, body).subscribe(
          (res: any) => {
            this.toast.success(
              status == "True"
                ? "Kích hoạt người dùng"
                : "Vô hiệu hoá người dùng",
              "Thành công",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
              }
            );
            row.active = res.user_is_active;
            // this.getAllUser();
          },
          (error) => {
            // this.toast.error(error.error, "Thất bại", {
            //   closeButton: true,
            //   positionClass: "toast-top-right",
            //   toastClass: "toast ngx-toastr",
            // });
          }
        );
      }
    });
  }
  get f() {
    return this.formResetPassword.controls;
  }

  onSubmit() {
    this.submitted = true;
    if (this.formResetPassword.invalid) return;
    this._userService
      .resetPassword(this.idUser, this.formResetPassword.get("password").value)
      .subscribe(
        (res) => {
          this.toast.success("Cấp lại mật khẩu", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.isPopoverEnabled = false;
        },
        (error) => {
          // this.toast.error(error.error, "Thất bại", {
          //   closeButton: true,
          //   positionClass: "toast-top-right",
          //   toastClass: "toast ngx-toastr",
          // });
        }
      );
  }
  cancel() {
    this.isPopoverEnabled = false;
  }
  ngOnDestroy() {
    this._unSubAll.next(null);
    this._unSubAll.complete();
  }
}

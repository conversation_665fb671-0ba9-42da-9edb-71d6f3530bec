import { Injectable } from "@angular/core";
import { ApiService } from "../../../../../../util/ServiceApi";
import { HttpClient } from "@angular/common/http";
import { environment } from "environments/environment";
import { BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class CampaignService extends ApiService {
  public data: BehaviorSubject<any> = new BehaviorSubject(null);
  constructor(private http: HttpClient) {
    super(http, "campaign");
  }
  createCampaign(body: any) {
    return this.http.post<any>(
      `${environment.apiUrl}/campaign/campaigns/`,
      body
    );
  }
  getAllCampaign(params: any) {
    return this.http.get<any>(`${environment.apiUrl}/campaign/campaigns`, {
      params,
    });
  }
  checkExistCode(params: any) {
    return this.http.get<any>(`${environment.apiUrl}/campaign/campaigns/check-code`, {
      params,
    });
  }
  deleteCampaign(idCampaign: any) {
    return this.http.delete<any>(
      `${environment.apiUrl}/campaign/campaigns/${idCampaign}`
    );
  }
  getOrgTree(params: any) {
    return this.http.get<any>(`${environment.apiUrl}/user/org-tree`, {
      params,
    });
  }
  getAllOrganization(params: any) {
    return this.http.get<any>(`${environment.apiUrl}/permission/organization`, {
      params,
    });
  }
  updateActiveStatus(idCampaign, body) {
    return this.http.put(
      `${environment.apiUrl}/campaign/campaigns/${idCampaign}/update_active_status/`,
      body
    );
  }
  updateCampaign(idCampaign: any, body: any) {
    return this.http.put<any>(
      `${environment.apiUrl}/campaign/campaigns/${idCampaign}/`,
      body
    );
  }
}

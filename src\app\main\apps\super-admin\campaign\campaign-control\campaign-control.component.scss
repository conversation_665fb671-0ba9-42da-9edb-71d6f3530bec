@import "@core/scss/angular/libs/select.component.scss";
@import "@core/scss/angular/libs/flatpickr.component.scss";
@import "primeicons/primeicons.css";

.input-with-icon {
  position: relative;
}

/* Base style cho nút */
.btn-icon-inside {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  border: none;
  background: transparent;
  padding: 0;
  cursor: pointer;
}

.btn-icon-inside img.dl-icon {
  width: 16px;
  height: 16px;
}

.input-with-icon:has(.left-icon) .form-control {
  padding-left: 40px;
}

.input-with-icon:has(.right-icon) .form-control {
  padding-right: 40px;
}

.btn-icon-inside.left-icon {
  left: 10px;
}

.btn-icon-inside.right-icon {
  right: 0;
}

// PAGIGN
/* Pagination gọn, không nuốt icon */
.pagination.custom-pager {
  gap: 6px;
}

.pagination.custom-pager .page-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0;
  line-height: 1;
  border-radius: 999px;
  border: 1px solid transparent;
  background: transparent;
  color: #475569;
}

.pagination.custom-pager .page-item:not(.active):not(.disabled) .page-link:hover {
  background: #f1f5f9;
}

.pagination.custom-pager .page-item.active .page-link {
  color: #0b5ed7 !important;
  background: transparent !important;
  font-weight: 700;
}

/* Disabled */
.pagination.custom-pager .page-item.disabled .page-link {
  // color: #cbd5e1;
  // background: transparent;
  // border-color: transparent;
  pointer-events: none;
}

.border-end {
  border-right: 1px solid grey;
}
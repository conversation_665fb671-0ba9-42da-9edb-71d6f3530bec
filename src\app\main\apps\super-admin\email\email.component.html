<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumb"></app-breadcrumb>
  <div class="card">
    <div class="card-header">
      <div class="col-5 pl-0 d-flex">
        <div class="input-group input-group-merge">
          <div class="input-group-prepend">
            <span class="input-group-text" id="search-name">
              <i data-feather="search"></i>
            </span>
          </div>
          <input
            type="text"
            class="form-control h-100"
            placeholder="Tìm kiếm theo code, tiêu đề..."
            aria-label="Tìm kiếm theo code, tiêu đề..."
            aria-describedby="search-name"
            [(ngModel)]="search"
            (ngModelChange)="searchChanged.next($event)"
          />
        </div>

        <div class="ml-1 min-width-200px">
          <ng-select
            [clearable]="true"
            placeholder="Lọc theo loại"
            [(ngModel)]="typeFilter"
            (change)="getListEmail()"
          >
            <ng-option value="transactional_email">Transactional Email</ng-option>
            <ng-option value="campaign_email">Campaign Email</ng-option>
          </ng-select>
        </div>
      </div>

      <div>
        <button type="button" class="btn btn-primary" (click)="navigateEmail()" rippleEffect>
          <span [data-feather]="'plus'" [class]="'mr-25'"></span>
          <span>Thêm mẫu Email</span>
        </button>
      </div>
      
    </div>
    <div class="card-body">
      <ngx-datatable
        #EmailManagementTable
        [rows]="listEmail"
        [rowHeight]="58"
        class="bootstrap core-bootstrap cursor"
        [columnMode]="ColumnMode.force"
        [headerHeight]="40"
        [footerHeight]="50"
        [scrollbarH]="true"
        [selectionType]="SelectionType.checkbox"
        [limit]="10"
        [count]="totalItem"
        [offset]="page - 1"
        [externalPaging]="true"
        (activate)="onActivate($event)"
        (select)="onSelect($event)"
        (page)="onPage($event)"
      >
        <!-- <ngx-datatable-column
          name="#"
          [width]="50"
          [sortable]="false"
          [canAutoResize]="false"
          [draggable]="false"
          [resizeable]="false"
        >
          <ng-template
            ngx-datatable-header-template
            let-value="value"
            let-allRowsSelected="allRowsSelected"
            let-selectFn="selectFn"
          >
            <div class="custom-control custom-checkbox">
              <input
                type="checkbox"
                class="custom-control-input"
                [checked]="allRowsSelected"
                (change)="selectFn(!allRowsSelected)"
                id="headerChkbxRef"
              />
              <label class="custom-control-label" for="headerChkbxRef"></label>
            </div>
          </ng-template>
          <ng-template
            ngx-datatable-cell-template
            let-rowIndex="rowIndex"
            let-value="value"
            let-isSelected="isSelected"
            let-onCheckboxChangeFn="onCheckboxChangeFn"
          >
            <div class="custom-control custom-checkbox">
              <input
                type="checkbox"
                class="custom-control-input"
                [checked]="isSelected"
                (change)="onCheckboxChangeFn($event)"
                id="rowChkbxRef{{ rowIndex }}"
              />
              <label class="custom-control-label" for="rowChkbxRef{{ rowIndex }}"></label>
            </div>
          </ng-template>
        </ngx-datatable-column> -->
        <ngx-datatable-column name="Code" prop="code"></ngx-datatable-column>
        <ngx-datatable-column name="Tiêu đề" prop="title"></ngx-datatable-column>
        <ngx-datatable-column name="Lần cuối chỉnh sửa">
          <ng-template ngx-datatable-cell-template let-row="row">
            <span>{{ formatRelativeTime(row.updated_at || row.created_at) }}</span>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Loại Email" prop="type">
          <ng-template ngx-datatable-cell-template let-row="row">
            <div 
              class="badge"
              [ngClass]="{
                'badge-light-primary': row.type === 'campaign_email',
                'badge-light-success': row.type === 'transactional_email'
              }">
              {{ typeLabelMap[row.type] || row.type }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Hành động">
          <ng-template ngx-datatable-cell-template let-row="row">
            <!-- <button type="button" [ngbTooltip]="'Sửa'" container="body" 
                    class="btn btn-icon btn-icon rounded-circle btn-flat-primary" 
                    (click)="navigateEmail(row.id)" rippleEffect>
              <span [data-feather]="'edit-2'"></span>
            </button> -->
            <button type="button" 
                    [ngbTooltip]="row.has_campaign
                      ? 'Không thể xóa email đã được gán vào chiến dịch' 
                      : 'Xóa'"
                    container="body" 
                    class="btn btn-icon btn-icon rounded-circle btn-flat-danger" 
                    [disabled]="row.has_campaign || row.type == 'transactional_email'"
                    (click)="deleteEmail(row)" rippleEffect>
              <span [data-feather]="'trash-2'"></span>
            </button>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
    </div>
  </div>
</div>

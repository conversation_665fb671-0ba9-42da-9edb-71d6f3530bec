<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumbDefault"></app-breadcrumb>
  <div class="card">
    <div class="card-body">
      <form class="form form-vertical px-1" [formGroup]="formGetOrgan">
        <div class="row">
          <div class="col-12 col-sm-4 col-xxl-4 ml-0 pl-0">
            <div class="input-group input-group-merge mb-2">
              <div class="input-group-prepend">
                <span class="input-group-text" id="basic-addon-search2"
                  ><i data-feather="search"></i
                ></span>
              </div>
              <input
                type="text"
                class="form-control"
                placeholder="Tìm kiếm theo tên ..."
                aria-label="Search..."
                aria-describedby="basic-addon-search2"
                formControlName="search"
              />
            </div>
          </div>
          <div class="col-xxl-2 col-12">
            <ng-select
              [items]="statusOrgan"
              bindLabel="label"
              bindValue="value"
              placeholder="Tất cả trạng thái"
              formControlName="status"
            >
            </ng-select>
          </div>
          <!-- <div class="col-xxl-2 col-12">
            <ng-select
              [items]="typeOrganization"
              bindLabel="label"
              bindValue="value"
              placeholder="Chọn loại tổ chức"
              formControlName="is_only_root"
            >
            </ng-select>
          </div> -->
          <div class="p-0 ml-auto">
            <div placement="top">
              <button
                type="button"
                rippleEffect
                class="btn btn-primary"
                (click)="addOrganization()"
              >
                {{ role == "SUPER_ADMIN" ? "Thêm tổ chức" : "Thêm phòng ban" }}
              </button>
            </div>
          </div>
        </div>
      </form>
      <ngx-datatable
        #tableRowDetails
        [rows]="listOrganization"
        [rowHeight]="58"
        class="bootstrap core-bootstrap cursor"
        [columnMode]="ColumnMode.force"
        [headerHeight]="40"
        [footerHeight]="50"
        [scrollbarH]="true"
        [limit]="limitTable"
        (activate)="onActivate($event)"
        [count]="totalItem"
        [offset]="page - 1"
        [externalPaging]="true"
        (page)="setPage($event)"
      >
        <ngx-datatable-column
          [name]="role == 'SUPER_ADMIN' ? 'Tên tổ chức' : 'Tên phòng ban'"
          prop="name"
          [width]="170"
        >
          <ng-template let-value="value" ngx-datatable-cell-template>
            <span [ngbTooltip]="value" container="body">{{ value }}</span>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          name="Trực thuộc"
          prop="parent_organization_name"
        ></ngx-datatable-column>
        <ngx-datatable-column
          name="Người đại diện"
          prop="represent_people"
        ></ngx-datatable-column>
        <ngx-datatable-column name="Địa chỉ" prop="address">
        </ngx-datatable-column>
        <ngx-datatable-column name="Số lượng User" prop="count">
          <ng-template let-value="value" ngx-datatable-cell-template>
            {{ value }} người
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Trạng thái" prop="status">
          <ng-template let-status="value" ngx-datatable-cell-template>
            <div
              class="badge badge-pill"
              [ngClass]="{
                'badge-light-success': status == 'active',
                'badge-light-danger': status == 'inactive'
              }"
            >
              {{ status == "active" ? "Hoạt động" : "Đã vô hiệu" }}
            </div>
          </ng-template>
        </ngx-datatable-column>

        <ngx-datatable-column name="Hành động" [sortable]="false" [width]="100">
          <ng-template ngx-datatable-cell-template let-row="row">
            <button 
              type="button" class="btn btn-icon btn-icon rounded-circle btn-flat-primary"
              ngbTooltip="Sửa" container="body"
              (click)="editOrganization(row)" rippleEffect>
              <span [data-feather]="'edit-2'"></span>
            </button>

            <button 
              *ngIf="row.status == 'active'"
              type="button" class="btn btn-icon btn-icon rounded-circle btn-flat-dark"
              ngbTooltip="Vô hiệu hoá" container="body"
              (click)="deactivate(row)" rippleEffect>
              <span [data-feather]="'slash'"></span>
            </button>

            <button 
              *ngIf="row.status == 'inactive'"
              type="button" class="btn btn-icon btn-icon rounded-circle btn-flat-dark"
              ngbTooltip="Kích hoạt lại" container="body"
              (click)="activate(row)" rippleEffect>
              <span [data-feather]="'check-circle'"></span>
            </button>

            <button 
              type="button" class="btn btn-icon btn-icon rounded-circle btn-flat-danger"
              ngbTooltip="Xóa" container="body"
              (click)="deleteOrganization(row)" rippleEffect>
              <span [data-feather]="'trash-2'"></span>
            </button>
          </ng-template>
        </ngx-datatable-column>
        <!-- <ngx-datatable-column
          name="Trạng thái hiệu lực"
          prop="tinh_trang_hieu_luc"
          [width]="200"
        >
          <ng-template
            let-status="value"
            let-row="row"
            ngx-datatable-cell-template
          >
            <div
              [ngClass]="[
                'badge',
                status === 'Còn hiệu lực'
                  ? 'badge-light-success'
                  : status === 'Hết hiệu lực một phần'
                  ? 'badge-light-warning'
                  : status === 'Hết hiệu lực toàn bộ'
                  ? 'badge-light-danger'
                  : status === 'Ngưng hiệu lực một phần'
                  ? 'badge-light-secondary'
                  : status === 'Không còn phù hợp'
                  ? 'badge-light-muted'
                  : status === 'Ngưng hiệu lực'
                  ? 'badge-light-muted'
                  : status === 'Chưa xác định'
                  ? 'badge-light-muted'
                  : 'badge-light-info'
              ]"
            >
              {{ status }}
            </div>
          </ng-template>
        </ngx-datatable-column> -->
      </ngx-datatable>
    </div>
  </div>
</div>
<ng-template #modalOrganizationControl let-modal>
  <app-organization-control
    [title]="title"
    [modal]="modal"
    [type]="type"
    [data]="data"
    [listOrganization]="listOrganization"
  ></app-organization-control>
</ng-template>

import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { EmailService } from '../email.service';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import Quill from 'quill';

@Component({
  selector: 'app-email-content',
  templateUrl: './email-content.component.html',
  styleUrls: ['./email-content.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class EmailContentComponent implements OnInit {
  public id: string;

  public breadcrumb: object;
  public form: FormGroup;
  public emailInfo: any;

  public editorModules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],       // format c<PERSON> bản
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],       // heading
      [{ 'font': [] }],                                // font
      [{ 'size': [] }],                                // size
      [{ 'color': [] }, { 'background': [] }],         // màu chữ + nền
      [{ 'script': 'sub' }, { 'script': 'super' }],    // subscript/superscript
      [{ 'align': [] }],                               // align
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],   // list
      [{ 'indent': '-1' }, { 'indent': '+1' }],        // indent
      ['blockquote', 'code-block'],                    // quote + code
      ['link', 'image', 'formula'],                    // link, media, formula
      ['clean']                                        // clear format
    ],
    syntax: false
  };  

  constructor(
    private _fb: FormBuilder,
    private _router: Router,
    private _route: ActivatedRoute,
    private _emailService: EmailService,
    private _toastSerive: ToastrService
  ) {
    const Parchment = Quill.import('parchment');
    const AlignStyle = new Parchment.Attributor.Style('align', 'text-align', {
      scope: Parchment.Scope.BLOCK,
      whitelist: ['left', 'right', 'center', 'justify'],
    });
    const FontStyle = new Parchment.Attributor.Style('font', 'font-family');
    const SizeStyle = new Parchment.Attributor.Style('size', 'font-size', {
      whitelist: ['small', 'large', 'huge'],
    });
    const ColorStyle = new Parchment.Attributor.Style('color', 'color');
    const BackgroundStyle = new Parchment.Attributor.Style('background', 'background-color');

    Quill.register(AlignStyle, true);
    Quill.register(FontStyle, true);
    Quill.register(SizeStyle, true);
    Quill.register(ColorStyle, true);
    Quill.register(BackgroundStyle, true);

    this._route.paramMap.subscribe(params => {
      this.id = params.get('id');
    });
  }

  setBreadCrumb() {
    this.breadcrumb = {
      links: [
        {
          name: "Quản lý nội dung email",
          // isHeader: true,
          isLink: true,
          link: "/super-admin/email_management"
        },
        {
          name: this.id ? "Cập nhật email" : "Thêm mẫu email",
        }
      ],
    };
  }

  getEmail() {
    if (!this.id) return;

    this._emailService.getEmail(this.id).subscribe(
      (res) => {
        this.emailInfo = res;
        this.form.patchValue({
          code: this.emailInfo.code,
          type: this.emailInfo.type,
          title: this.emailInfo.title,
          content: this.emailInfo.content,
          // footer: this.emailInfo.footer
        });

        this.form.get('code')?.disable();
        this.form.get('type')?.disable();
      }
    )
  }

  initForm() {
    this.form = this._fb.group({
      code: ['', Validators.required],
      type: ['campaign_email', Validators.required],
      title: ['', Validators.required],
      content: ['', Validators.required],
      // footer: ['', Validators.required],
    });
  }

  submitForm() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this._toastSerive.error('Vui lòng điền đầy đủ thông tin bắt buộc', 'Thất bại!');
      return;
    }
    
    const formData = this.form.value;

    if (this.id) {
      this._emailService.updateEmail(this.id, formData).subscribe(
        (res) => {
          this._toastSerive.success(`Đã update Email ${formData.code}`, 'Thành công!')
          this._router.navigate(['/super-admin/email_management/']);
        },
        (err) => {
          console.error('Update failed', err)
          this._toastSerive.error(`Có lỗi xảy ra, vui lòng thử lại`, 'Thất bại!')
        }
      )
    } else {
      this._emailService.createEmail(formData).subscribe(
        (res) => {
          this._toastSerive.success(`Đã tạo Email ${formData.code}`, 'Thành công!')
          this._router.navigate(['/super-admin/email_management/']);
        },
        (err) => {
          console.error('Update failed', err)
          this._toastSerive.error('Có lỗi xảy ra, vui lòng thử lại', 'Thất bại!')
        }
      )
    }
  }

  cancel() {
    this._router.navigate(['/super-admin/email_management/']);
  }
  
  ngOnInit(): void {
    this.setBreadCrumb();
    this.initForm();
    this.getEmail();
  }
}

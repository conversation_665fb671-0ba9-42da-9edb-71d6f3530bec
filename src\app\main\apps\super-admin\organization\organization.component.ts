import { Component, OnInit, ViewChild, ViewEncapsulation } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { FormType } from "app/models/FormType";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { debounceTime, takeUntil } from "rxjs/operators";
import Swal from "sweetalert2";
import { OrganizationService } from "./organization.service";
import { AuthenticationService } from "app/auth/service";

@Component({
  selector: "app-organization",
  templateUrl: "./organization.component.html",
  styleUrls: ["./organization.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class OrganizationComponent implements OnInit {
  @ViewChild("modalOrganizationControl")
  modalOrganizationControl: NgbActiveModal;
  public listOrganization: [] = [];
  public ColumnMode = ColumnMode;
  public limitTable: number = 12;
  public totalItem: number = 0;
  public breadcrumbDefault: object;
  title: string;
  type: FormType;
  data: any;
  public page = 1;
  public _unsubAll: Subject<any> = new Subject();
  public formGetOrgan: FormGroup;
  public statusOrgan = [
    { value: "active", label: "Hoạt động" },
    { value: "inactive", label: "Đã vô hiệu" },
  ];
  public typeOrganization = [
    { value: true, label: "Tổ chức" },
    { value: false, label: "Phòng ban" },
  ];
  public role: string;
  constructor(
    private _organService: OrganizationService,
    private modalService: NgbModal,
    private toast: ToastrService,
    private fb: FormBuilder,
    private _authenService: AuthenticationService
  ) {
    this.formGetOrgan = fb.group({
      page: 1,
      search: "",
      status: null,
      root_organization_id: null,
    });
    this.role = _authenService.currentUserValue.role;
    if (this.role == "ADMIN") {
      this.formGetOrgan.patchValue({
        root_organization_id: localStorage.getItem("organization_id"),
      });
    } else {
      this.formGetOrgan.removeControl("root_organization_id");
    }
  }

  ngOnInit(): void {
    this.breadcrumbDefault = {
      links: [
        {
          name:
            this.role == "SUPER_ADMIN"
              ? "Quản lý tổ chức"
              : "Quản lý phòng ban",
          isHeader: true,
        },
      ],
    };
    // if (this.role == "ADMIN") {
    //   this.formGetOrgan.removeControl("is_only_root");
    // }
    this._organService.reloadData
      .pipe(takeUntil(this._unsubAll))
      .subscribe((res) => {
        if (res) {
          this.getAllOrganization();
        }
      });
    Object.keys(this.formGetOrgan.controls).forEach((controlName) => {
      this.formGetOrgan
        .get(controlName)
        ?.valueChanges.pipe(takeUntil(this._unsubAll), debounceTime(300))
        .subscribe((value) => {
          if (controlName != "page") {
            this.formGetOrgan.get("page").setValue(1, { emitEvent: false });
            this.page = 1;
          }

          this.getAllOrganization();
        });
    });
    this.getAllOrganization();
  }
  getAllOrganization() {
    this._organService
      .getAllOrganization(this.formGetOrgan.value)
      .subscribe((res) => {
        this.listOrganization = res.results;
        this.totalItem = res.count;
      });
  }
  addOrganization() {
    this.modalOpen(
      this.modalOrganizationControl,
      this.role == "SUPER_ADMIN" ? "Thêm tổ chức" : "Thêm phòng ban",
      null,
      FormType.Create,
      "sm"
    );
  }
  editOrganization(data) {
    this.modalOpen(
      this.modalOrganizationControl,
      this.role == "SUPER_ADMIN" ? "Cập nhật tổ chức" : "Cập nhật phòng ban",
      data,
      FormType.Update,
      "sm"
    );
  }
  onActivate(event) {
    // if (event.type == "click") {
    //   console.log(event);
    // }
  }
  modalOpen(modalSM, title: string, data: any, type: FormType, size: string) {
    this.modalService.open(modalSM, {
      centered: true,
      size: size,
    });
    this.title = title;
    this.type = type;
    this.data = data;
  }
  deleteOrganization(organ) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this._organService.destroy(organ.id).subscribe((res) => {
          this.toast.success(
            this.role == "SUPER_ADMIN" ? "Đã xoá tổ chức" : "Đã xoá phòng ban",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
          this.getAllOrganization();
        });
      }
    });
  }
  setPage({ offset }) {
    this.page = offset + 1;
    this.formGetOrgan.patchValue({ page: offset + 1 });
  }
  deactivate(data) {
    console.log(data)
    Swal.fire({
      title: `Vô hiệu hoá tổ chức ${data.name}?`,
      text: `Các tài khoản thuộc tổ chức ${data.name} cũng sẽ bị vô hiệu hóa`,
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Đồng ý",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this._organService.updateStatusOrgan(data.id, "inactive").subscribe(
          (res) => {
            this.toast.success(
              this.role == "SUPER_ADMIN"
                ? "Đã vô hiệu hoá tổ chức"
                : "Đã vô hiệu hoá phòng ban",
              "Thành công",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
              }
            );
            data.status = "inactive";
          },
          (error) => {
            this.toast.error(error.error, "Thất bại", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          }
        );
      }
    });
  }
  activate(data) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn kích hoạt lại?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Đồng ý",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this._organService.updateStatusOrgan(data.id, "active").subscribe(
          (res) => {
            this.toast.success(
              this.role == "SUPER_ADMIN"
                ? "Đã kích hoạt tổ chức"
                : "Đã kích hoạt phòng ban",

              "Thành công",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
              }
            );
            data.status = "active";
          },
          (error) => {
            this.toast.error(error.error, "Thất bại", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          }
        );
      }
    });
  }
  ngOnDestroy() {
    this._unsubAll.next(null);
    this._unsubAll.complete();
  }
}

import { Component, OnInit, ViewChild, ViewEncapsulation } from "@angular/core";
import { Router } from "@angular/router";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { FormType } from "app/models/FormType";
import { CampaignService } from "./campaign.service";
import Swal from "sweetalert2";
import { Email, EmailStatus } from "app/models/TypeEmail";
import { ToastrService } from "ngx-toastr";
import { BehaviorSubject, combineLatest, Subject, merge } from "rxjs";
import {
  debounceTime,
  distinctUntilChanged,
  map,
  switchMap,
  takeUntil,
  withLatestFrom,
} from "rxjs/operators";
import { Campaign, CampaignStatus } from "app/models/StatusCampaign";
import { WebSocketService } from "app/auth/service/webSocket.service";
@Component({
  selector: "app-campaign",
  templateUrl: "./campaign.component.html",
  styleUrls: ["./campaign.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class CampaignComponent implements OnInit {
  @ViewChild("modalCampaignControl") modalCampaignControl: NgbActiveModal;

  public listCampaign: any = [];
  public ColumnMode = ColumnMode;
  public limitTable: number = 10;
  public totalItem: number = 0;
  public page: number = 1;
  public breadcrumbDefault: object;
  public representatives = [];
  title: string;
  type: FormType;
  data: any;
  public campaign = Campaign;
  public email = Email;
  public campaignStatus = CampaignStatus;
  public searchText: any = "";
  public selectedEmail: EmailStatus | null = null;
  public selectedCampaign: CampaignStatus | null = null;
  public params: any = null;
  private destroy$ = new Subject<void>();
  private search$ = new Subject<string>();
  private email$ = new BehaviorSubject<string>("");
  private campaign$ = new BehaviorSubject<string>("");
  private page$ = new BehaviorSubject<number>(1);
  private refresh$ = new Subject<void>();
  private _unSubAll: Subject<any> = new Subject();

  constructor(
    private _campaignService: CampaignService,
    private router: Router,
    private modalService: NgbModal,
    private toastSerive: ToastrService,
    private webSocketService: WebSocketService
  ) {}

  ngOnInit(): void {
    this.breadcrumbDefault = {
      links: [
        { 
          name: "Quản lý chiến dịch", 
          isHeader: true
        }
      ],
    };
    this.webSocketService.messageSubject
      .pipe(takeUntil(this._unSubAll))
      .subscribe((res) => {
        if (!res) return;
        const dataMsg = res.data;
        if (dataMsg.type === "campaign_notify") {
          if (dataMsg.event === "ONGOING") {
            for (const it of this.listCampaign) {
              if (dataMsg.data && dataMsg.data.campaign_id === it.id) {
                it.status = dataMsg.event;
              }
            }
          }
          if (dataMsg.event === "DONE") {
            this.refresh$.next(null);
          }
        }
      });
    const current_user = JSON.parse(
      localStorage.getItem("current_User") || "{}"
    );

    const params$ = combineLatest([
      this.search$.pipe(debounceTime(400), distinctUntilChanged()),
      this.email$,
      this.campaign$,
      this.page$,
    ]).pipe(
      map(([search, email, campaign, page]) => {
        const baseParams: any = {
          user_id: current_user?.id,
          page,
          email_status: email || "",
          search: (search || "").trim(),
        };

        if (campaign === 'DISABLED') {
          baseParams.is_active = false;
        } else {
          baseParams.campaign_status = campaign || "";
        }

        return baseParams;
      }),
      distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b))
    );

    merge(
      params$,
      this.refresh$.pipe(
        withLatestFrom(params$),
        map(([_, params]) => params)
      )
    )
      .pipe(
        switchMap((params) => this._campaignService.getAllCampaign(params)),
        takeUntil(this.destroy$)
      )
      .subscribe((res) => {
        if (res?.results) {
          this.listCampaign = res.results;
          this.totalItem = res.count;
        } else {
          this.listCampaign = res || [];
          this.totalItem = Array.isArray(res) ? res.length : 0;
        }
      });

    // seed lần đầu
    this.search$.next("");
  }
  onSearchChange(v: string) {
    this.page$.next(1);
    this.searchText = v;
    this.search$.next(v);
  }

  onEmailChange(event: any) {
    const value = typeof event === "string" ? event : event?.value ?? "";
    this.selectedEmail = value;
    this.page$.next(1);
    this.email$.next(value);
  }

  onCampaignChange(event: any) {
    this.selectedCampaign = event;
    this.page$.next(1);
    this.campaign$.next(event);
  }

  setPage({ offset }) {
    this.page = offset + 1;
    this.page$.next(this.page);
  }
  getAllCampaign() {
    const current_user = JSON.parse(localStorage.getItem("current_User"));
    const params = {
      user_id: current_user.id,
      page: this.page,
      campaign_status: this.selectedCampaign || "",
      email_status: this.selectedEmail || "",
      search: this.searchText.trim() || "",
    };
    this._campaignService.getAllCampaign(params).subscribe((res) => {
      if (res.results) {
        this.listCampaign = res.results;
        this.totalItem = res.count;
      }
    });
  }
  addCampaign() {
    this.router.navigate(["super-admin/campaign/campaign-control"], {
      queryParams: {
        type: "add",
      },
      queryParamsHandling: "merge",
    });
  }
  editCampaign(data) {
    this._campaignService.data.next(data);
    this.router.navigate(["super-admin/campaign/campaign-control"], {
      queryParams: {
        type: "update",
      },
      queryParamsHandling: "merge",
    });
  }
  deleteCampaign(cmp) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this._campaignService.deleteCampaign(cmp.id).subscribe((res) => {
          this.toastSerive.success(
            "Đã xoá chiến dịch thành công",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
          this.refresh$.next(null);
        });
      }
    });
  }
  updateActiveStatus(cmp, is_active: boolean) {
    console.log(is_active)
    const statusMess =
      is_active == false
        ? "Vô hiệu hóa"
        : "Kích hoạt lại";
    Swal.fire({
      title: `${statusMess} chiến dịch?`,
      // text: "Bạn vẫn có thể hoàn tác",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: statusMess,
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        const body = {
          is_active: is_active,
        };
        this._campaignService.updateActiveStatus(cmp.id, body).subscribe((res) => {
          this.toastSerive.success(
            `Đã ${statusMess} chiến dịch thành công`,
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
          this.refresh$.next(null);
        });
      }
    });
  }

  addPosition() {
    this.router.navigate(["super-admin/user/user-control"], {
      queryParams: {
        type: "add",
      },
      queryParamsHandling: "merge",
    });
  }
  onActivate(event) {
    if (event.type == "click" && event.column.name != "Hành động") {
      this.editCampaign(event.row)
    }
  }

  cancel() {}

  ngOnDestroy() {
    this._unSubAll.next(null);
    this._unSubAll.complete();
    this.destroy$.next(null);
    this.destroy$.complete();
  }
}

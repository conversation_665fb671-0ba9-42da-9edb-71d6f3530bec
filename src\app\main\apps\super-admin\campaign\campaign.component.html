<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumbDefault"></app-breadcrumb>
  <div class="card">
    <div class="card-body">
      <form class="form form-vertical px-1">
        <div class="row">
          <div class="col-12 col-sm-4 col-xxl-4 ml-0 pl-0">
            <div class="input-group input-group-merge mb-2">
              <div class="input-group-prepend">
                <span class="input-group-text" id="basic-addon-search2">
                  <i data-feather="search"></i>
                </span>
              </div>
              <input
                type="text"
                class="form-control"
                placeholder="Tìm kiếm theo tiêu đề..."
                aria-label="Search..."
                aria-describedby="basic-addon-search2"
                [ngModelOptions]="{ standalone: true }"
                [(ngModel)]="searchText"
                (ngModelChange)="onSearchChange($event)"
              />
            </div>
          </div>
          <div class="col-12 col-sm-2 ml-0 pl-0">
            <fieldset class="form-group">
              <ng-select
                bindValue="id"
                bindLabel="name"
                [clearable]="true"
                placeholder="Tất cả trạng thái"
                [(ngModel)]="selectedCampaign"
                [ngModelOptions]="{ standalone: true }"
                (ngModelChange)="onCampaignChange($event)"
              >
                <ng-option *ngFor="let item of campaign" [value]="item.id">
                  {{ item.name }}
                </ng-option>
              </ng-select>
            </fieldset>
          </div>
          <div class="col-12 col-sm-2 ml-0 pl-0">
            <fieldset class="form-group">
              <ng-select
                [items]="email"
                bindValue="id"
                bindLabel="name"
                [clearable]="true"
                placeholder="Tất cả loại"
                [(ngModel)]="selectedEmail"
                [ngModelOptions]="{ standalone: true }"
                (ngModelChange)="onEmailChange($event)"
              >
              </ng-select>
            </fieldset>
          </div>

          <div class="p-0 ml-auto">
            <div placement="top">
              <button
                type="button"
                rippleEffect
                class="btn btn-primary"
                (click)="addCampaign()"
              >
                Thêm chiến dịch
              </button>
            </div>
          </div>
        </div>
      </form>
      <ngx-datatable
        #tableRowDetails
        [rows]="listCampaign"
        [rowHeight]="58"
        class="bootstrap core-bootstrap cursor"
        [columnMode]="ColumnMode.force"
        [headerHeight]="40"
        [footerHeight]="50"
        [scrollbarH]="true"
        [limit]="limitTable"
        (activate)="onActivate($event)"
        [count]="totalItem"
        [offset]="page - 1"
        [externalPaging]="true"
        (page)="setPage($event)"
      >
        
        <ngx-datatable-column name="Code" prop="code" [width]="50">
        </ngx-datatable-column>

        <ngx-datatable-column name="Tiêu đề" prop="name" [width]="250">
        </ngx-datatable-column>
        <ngx-datatable-column name="Số người nhận" prop="tong_so_nguoi" [width]="140">
          <ng-template
            let-status="value"
            let-row="row"
            ngx-datatable-cell-template
          >
            {{ row.tong_so_nguoi }} người
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Loại email" prop="email_type" [width]="150">
          <ng-template
            let-status="value"
            let-row="row"
            ngx-datatable-cell-template
          >
            <div
              class="badge badge-pill"
              [ngClass]="{
                'badge-light-primary': row.email_type === 'Campaign Email',
                'badge-light-success': row.email_type === 'Transactional Email'
              }"
            >
              {{ row.email_type }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Trạng thái" prop="status" [width]="100">
          <ng-template
            let-status="value"
            let-row="row"
            ngx-datatable-cell-template
          >
            <div
              class="badge badge-pill"
              [ngClass]="{
                'badge-light-info': row.status === campaignStatus.STATUS_ONGOING,
                'badge-light-warning': row.status === campaignStatus.STATUS_SCHEDULED,
                'badge-light-dark': row.status === campaignStatus.STATUS_DRAFT,
                'badge-light-success': row.status === campaignStatus.STATUS_DONE
              }"
            >
              {{
                  row.status === campaignStatus.STATUS_DONE
                  ? "Đã gửi"
                  : row.status === campaignStatus.STATUS_DRAFT
                  ? "Nháp"
                  : row.status === campaignStatus.STATUS_SCHEDULED
                  ? "Đã lên lịch"
                  : row.status === campaignStatus.STATUS_ONGOING
                  ? "Đang gửi"
                  : ""
              }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Hiệu lực" prop="is_active" [width]="90">
          <ng-template
            let-status="value"
            let-row="row"
            ngx-datatable-cell-template
          >
            <div
              class="badge badge-pill"
              [ngClass]="{
                'badge-light-danger': !row.is_active,
                'badge-light-success': row.is_active
              }"
            >
              {{
                !row.is_active
                  ? "Đã vô hiệu hóa"
                  : row.is_active
                  ? "Hoạt động"
                  : ""
              }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Hành động" [width]="70" [sortable]="false">
          <ng-template ngx-datatable-cell-template let-row="row">
            <div>
              <button 
                *ngIf="
                  row.is_active &&
                  ![campaignStatus.STATUS_ONGOING, campaignStatus.STATUS_DONE].includes(row.status)"
                type="button" 
                class="btn btn-icon btn-icon rounded-circle btn-flat-primary" 
                [ngbTooltip]="'Vô hiệu hoá'"
                container="body"
                (click)="updateActiveStatus(row, !row.is_active)"
                rippleEffect
              >
                <span [data-feather]="'slash'"></span>
              </button>

              <button 
                *ngIf="
                  !row.is_active &&
                  ![campaignStatus.STATUS_ONGOING, campaignStatus.STATUS_DONE].includes(row.status)"
                type="button" 
                class="btn btn-icon btn-icon rounded-circle btn-flat-primary" 
                ngbTooltip="Kích hoạt lại"
                container="body"
                (click)="updateActiveStatus(row, !row.is_active)"
                rippleEffect
              >
                <span [data-feather]="'check-circle'"></span>
              </button>

              <button 
                *ngIf="![campaignStatus.STATUS_ONGOING, campaignStatus.STATUS_DONE].includes(row.status)"
                type="button" 
                class="btn btn-icon btn-icon rounded-circle btn-flat-danger" 
                [ngbTooltip]="'Xóa'"
                container="body"
                (click)="deleteCampaign(row)"
                rippleEffect
              >
                <span [data-feather]="'trash-2'"></span>
              </button>
            </div>
          </ng-template>
        </ngx-datatable-column>
      </ngx-datatable>
    </div>
  </div>
</div>
<ng-template #modalDepartmentControl let-modal>
  <app-department-control
    [title]="title"
    [modal]="modal"
    [type]="type"
    [row]="row"
  ></app-department-control>
</ng-template>

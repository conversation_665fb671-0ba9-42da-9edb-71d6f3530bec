<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumbDefault"></app-breadcrumb>
  <div class="card">
    <div class="card-body">
      <form class="form form-vertical px-1" [formGroup]="formGetUsers">
        <div class="row">
          <div class="col-12 col-sm-4 col-xxl-4 ml-0 pl-0">
            <div class="input-group input-group-merge mb-2">
              <div class="input-group-prepend">
                <span class="input-group-text" id="basic-addon-search2">
                  <i data-feather="search"></i>
                </span>
              </div>
              <input
                formControlName="search"
                type="text"
                class="form-control"
                placeholder="Tìm kiếm theo tên hoặc email..."
                aria-label="Search..."
                aria-describedby="basic-addon-search2"
              />
            </div>
          </div>
          <div class="col-12 col-sm-2 ml-0 pl-0">
            <fieldset class="form-group">
              <ng-select
                formControlName="organization_id"
                [items]="listOrgan"
                bindValue="id"
                bindLabel="name"
                [clearable]="role == 'SUPER_ADMIN' ? true : false"
                [placeholder]="
                  role == 'SUPER_ADMIN' ? 'Tất cả tổ chức' : 'Tất cả phòng ban'
                "
              ></ng-select>
            </fieldset>
          </div>
          <div class="col-12 col-sm-2 ml-0 pl-0">
            <fieldset class="form-group">
              <ng-select
                formControlName="active"
                [items]="listStatusUser"
                bindLabel="label"
                bindValue="value"
                [clearable]="true"
                placeholder="Tất cả trạng thái"
              ></ng-select>
            </fieldset>
          </div>

          <div class="p-0 ml-auto">
            <div placement="top">
              <button
                type="button"
                rippleEffect
                class="btn btn-primary"
                (click)="addPosition()"
              >
                Thêm người dùng
              </button>
            </div>
          </div>
        </div>
      </form>
      <ngx-datatable
        #tableRowDetails
        [rows]="listUser"
        [rowHeight]="58"
        class="bootstrap core-bootstrap cursor"
        [columnMode]="ColumnMode.force"
        [headerHeight]="40"
        [footerHeight]="50"
        [scrollbarH]="true"
        [limit]="limitTable"
        (activate)="onActivate($event)"
        [count]="totalItem"
        [offset]="page - 1"
        [externalPaging]="true"
        (page)="setPage($event)"
      >
        <ngx-datatable-column name="Họ tên" prop="fullname" [width]="150">
        </ngx-datatable-column>
        <ngx-datatable-column name="Email" prop="email" [width]="150">
        </ngx-datatable-column>

        <!-- <ngx-datatable-column name="Số điện thoại" prop="phone" [width]="150">
        </ngx-datatable-column> -->
        <ngx-datatable-column
          [name]="role == 'SUPER_ADMIN' ? 'Tổ chức' : 'Phòng ban'"
          prop="organization_memberships"
          [width]="150"
        >
          <ng-template
            let-status="value"
            let-row="row"
            ngx-datatable-cell-template
          >
            {{ row.organization_memberships[0]?.organization_name }}
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column
          name="Chức vụ"
          prop="position.title"
          [width]="150"
        >
        </ngx-datatable-column>

        <ngx-datatable-column name="Vai trò" prop="role" [width]="150">
          <ng-template
            let-status="value"
            let-row="row"
            ngx-datatable-cell-template
          >
            <div
              class="badge badge-pill"
              [ngClass]="{
                'badge-light-dark': row.system_role == 'USER',
                'badge-light-primary': row.system_role == 'ADMIN',
                'badge-light-success': row.system_role == 'SUPER_ADMIN'
              }"
            >
              {{
                row.system_role == "USER"
                  ? "Người dùng"
                  : row.system_role == "ADMIN"
                  ? "Quản trị viên"
                  : "Quản trị cấp cao"
              }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Trạng thái" prop="active" [width]="200">
          <ng-template ngx-datatable-cell-template let-row="row">
            <div
              class="badge badge-pill"
              [ngClass]="{
                'badge-light-success': row.active === true,
                'badge-light-danger': row.active === false
              }"
            >
              {{ row.active ? "Hoạt động" : "Đã khoá" }}
            </div>
          </ng-template>
        </ngx-datatable-column>
        <ngx-datatable-column name="Hành động" [width]="120" [sortable]="false">
          <ng-template ngx-datatable-cell-template let-row="row">
            <span ngbDropdown container="body">
              <a
                ngbDropdownToggle
                href="javascript:void(0);"
                class="hide-arrow"
                id="dropdownBrowserState"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
                placement="top"
                ngbTooltip="Sửa"
                container="body"
                (click)="editOrganization(row)"
              >
                <i data-feather="edit-2" class="mx-50"></i>
              </a>
              <a
                *ngIf="row.active"
                ngbDropdownToggle
                href="javascript:void(0);"
                class="hide-arrow color-333"
                id="dropdownBrowserState"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
                placement="top"
                container="body"
                ngbTooltip="Vô hiệu hoá"
                (click)="updateStatus(row, 'False')"
              >
                <i data-feather="slash" class="mx-50"></i>
              </a>
              <a
                *ngIf="!row.active"
                ngbDropdownToggle
                href="javascript:void(0);"
                class="hide-arrow"
                id="dropdownBrowserState"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
                placement="top"
                container="body"
                ngbTooltip="Kích hoạt lại"
                (click)="updateStatus(row, 'True')"
              >
                <i data-feather="check-circle" class="mx-50"></i>
              </a>
              <a
                ngbDropdownToggle
                href="javascript:void(0);"
                class="hide-arrow color-333"
                id="dropdownBrowserState"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
                placement="top"
                container="body"
                ngbTooltip="Cấp lại mật khẩu"
                (click)="isPopoverEnabled = true"
                [ngbPopover]="isPopoverEnabled ? resetPassword : null"
                #popover="ngbPopover"
                [autoClose]="'outside'"
              >
                <i data-feather="settings" class="mx-50"></i>
              </a>
              <a
                ngbDropdownToggle
                href="javascript:void(0);"
                class="hide-arrow color-333"
                id="dropdownBrowserState"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
                placement="top"
                container="body"
                ngbTooltip="Xóa"
                (click)="deleteUser(row)"
              >
                <i data-feather="trash-2" class="mx-50"></i>
              </a>
            </span>
          </ng-template>
        </ngx-datatable-column>
        <!-- <ngx-datatable-column
          name="Trạng thái hiệu lực"
          prop="tinh_trang_hieu_luc"
          [width]="200"
        >
          <ng-template
            let-status="value"
            let-row="row"
            ngx-datatable-cell-template
          >
            <div
              [ngClass]="[
                'badge',
                status === 'Còn hiệu lực'
                  ? 'badge-light-success'
                  : status === 'Hết hiệu lực một phần'
                  ? 'badge-light-warning'
                  : status === 'Hết hiệu lực toàn bộ'
                  ? 'badge-light-danger'
                  : status === 'Ngưng hiệu lực một phần'
                  ? 'badge-light-secondary'
                  : status === 'Không còn phù hợp'
                  ? 'badge-light-muted'
                  : status === 'Ngưng hiệu lực'
                  ? 'badge-light-muted'
                  : status === 'Chưa xác định'
                  ? 'badge-light-muted'
                  : 'badge-light-info'
              ]"
            >
              {{ status }}
            </div>
          </ng-template>
        </ngx-datatable-column> -->
      </ngx-datatable>
    </div>
  </div>
</div>
<ng-template #resetPassword>
  <form
    class="rp-card mx-auto"
    [formGroup]="formResetPassword"
    (ngSubmit)="onSubmit()"
  >
    <div class="mb-2 form-label fw-semibold">Mật khẩu mới</div>

    <div class="input-group input-group-merge form-password-toggle">
      <input
        [type]="mergedPwdShow ? 'text' : 'password'"
        class="form-control"
        name="password"
        autocomplete="off"
        formControlName="password"
        id="basic-default-password1"
        placeholder="Nhập"
        aria-describedby="basic-default-password1"
      />
      <div class="input-group-append" (click)="mergedPwdShow = !mergedPwdShow">
        <span class="input-group-text cursor-pointer"
          ><i
            class="feather"
            [ngClass]="{
              'icon-eye-off': mergedPwdShow,
              'icon-eye': !mergedPwdShow
            }"
          ></i
        ></span>
      </div>
    </div>
    <div class="col-sm-12 p-0">
      <div
        *ngIf="(submitted || f.password?.touched) && f.password?.errors"
        class="invalid-form"
      >
        <small
          *ngIf="f.password?.errors['minlength']"
          class="form-text text-danger"
        >
          Mật khẩu phải có ít nhất 6 ký tự
        </small>

        <small
          *ngIf="
            f.password?.errors['pattern'] && !f.password?.errors['minlength']
          "
          class="form-text text-danger"
        >
          Mật khẩu phải có ít nhất 1 chữ in hoa và 1 ký tự đặc biệt
        </small>
      </div>
    </div>

    <div class="form-text mt-2">
      Để trống mật khẩu sẽ đặt mật khẩu ngẫu nhiên được gửi đến mail của người
      dùng
    </div>

    <div class="d-flex justify-content-end">
      <button type="button" class="btn btn-secondary mr-50" (click)="cancel()">
        Hủy
      </button>
      <button type="submit" class="btn btn-primary">Xác nhận</button>
    </div>
  </form>
</ng-template>

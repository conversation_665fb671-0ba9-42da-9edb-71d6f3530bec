<div class="modal-header border-0">
  <h4 class="modal-title">Thêm người nhận</h4>
  <div class="text-primary fw-semibold ms-auto">
    <PERSON><PERSON> chọn {{ selectedCount }}
  </div>
</div>

<div class="modal-body">
  <div class="mb-1 position-relative">
    <div class="input-with-icon position-relative">
      <button type="button" class="btn-icon-inside left-icon">
        <i data-feather="search"></i>
      </button>

      <input
        type="text"
        class="form-control"
        placeholder="Tì<PERSON> ki<PERSON><PERSON> theo tổ chức, người dùng"
        (input)="searchText = $any($event.target).value || ''"
      />
    </div>
  </div>

  <div class="tree-wrap">
    <ng-container *ngFor="let n of data">
      <ng-container
        *ngTemplateOutlet="treeNodeTpl; context: { $implicit: n, level: 0 }"
      ></ng-container>
    </ng-container>
  </div>

  <ng-template #treeNodeTpl let-node let-level="level">
    <div
      *ngIf="matches(node, searchText)"
      class="tree-row"
      [style.paddingLeft.px]="12 + level * 20"
      [class.has-children]="node.children?.length"
      [class.is-partial]="node.partial"
    >
      <button
        type="button"
        class="toggle"
        *ngIf="node.children?.length"
        (click)="toggleExpand(node)"
        [attr.aria-label]="node.expanded ? 'Collapse' : 'Expand'"
      >
        <i
          class="feather"
          [ngClass]="{
            'icon-chevron-down': node.expanded,
            'icon-chevron-right': !node.expanded
          }"
        ></i>
      </button>

      <i
        class="feather node-icon"
        [ngClass]="{
          'icon-briefcase': node.type === 'org',
          'icon-folder': node.type === 'group',
          'icon-user': node.type === 'user'
        }"
      ></i>

      <div class="node-text">
        <span class="node-name" [class.fw-semibold]="node.type !== 'user'">
          <strong>{{ node.name }}</strong>
          <ng-container *ngIf="node.email">
            <br />
            <i>{{ node.email }}</i>
          </ng-container>
        </span>
        <span class="node-count" *ngIf="node.type !== 'user' && node.count">
          ({{ node.count }})
        </span>
      </div>

      <div class="checkbox-right">
        <input
          type="checkbox"
          class="form-check-input"
          [checked]="node.checked"
          [indeterminate]="node.partial"
          (change)="onCheck(node, $event.target.checked)"
        />
      </div>
    </div>

    <div *ngIf="node.children?.length && node.expanded">
      <ng-container *ngFor="let c of node.children">
        <ng-container
          *ngTemplateOutlet="
            treeNodeTpl;
            context: { $implicit: c, level: level + 1 }
          "
        ></ng-container>
      </ng-container>
    </div>
  </ng-template>
</div>

<div class="modal-footer border-0">
  <button type="button" class="btn btn-secondary" (click)="cancel()">
    Hủy
  </button>
  <button type="button" class="btn btn-primary" (click)="confirm()">
    Xác nhận
  </button>
</div>

import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { environment } from "environments/environment";
import { Observable, of } from "rxjs";
import { catchError } from "rxjs/operators";

export interface RequestLogPayload {
  user_id?: number | string | null;
  feature_name: string;
  method?: string;
  status_code?: number;
  response?: string | null;
  parameters?: any; 
  meta?: any;
}

@Injectable({ providedIn: "root" })
export class RequestLogService {
  private endpoint = `${environment.apiUrl}/request-logs/ingest`;

  constructor(private http: HttpClient) {}

  ingest(payload: RequestLogPayload): Observable<any> {
    return this.http.post(this.endpoint, payload).pipe(
      catchError(() => of(null))
    );
  }
  slimCompareParams(postBody: any) {
    const p = postBody ?? {};
    if (p.item1 || p.item2) {
      return {
        POST: {
          item1_id: p.item1.id,
          item2_id: p.item2.id,
          messages_count: Array.isArray(p.messages) ? p.messages.length : 0,
        },
      };
    }

    // fallback
    return { POST: p };
  }
}

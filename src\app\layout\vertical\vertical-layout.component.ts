import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from "@angular/core";

import { Subject } from "rxjs";
import { filter, takeUntil } from "rxjs/operators";

import { NavigationEnd, Router } from "@angular/router";
import { CoreConfigService } from "@core/services/config.service";
import { AuthenticationService } from "app/auth/service";

@Component({
  selector: "vertical-layout",
  templateUrl: "./vertical-layout.component.html",
  styleUrls: ["./vertical-layout.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class VerticalLayoutComponent implements OnInit, OnDestroy {
  coreConfig: any;
  public role: string = "";
  // Private
  private _unsubscribeAll: Subject<any>;
  public isUrlAdmin: boolean = false;
  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   */
  constructor(
    private _coreConfigService: CoreConfigService,
    private _authService: AuthenticationService,
    private router: Router
  ) {
    // Set the private defaults
    this._unsubscribeAll = new Subject();
    this.role = _authService.currentUserValue?.role;

    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        if (event.urlAfterRedirects.includes("super-admin")) {
          this.isUrlAdmin = true;
        } else {
          this.isUrlAdmin = false;
        }
      });
  }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    // Subscribe to config changes
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;
      });
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }
}

import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ColumnMode, DatatableComponent, SelectionType } from '@swimlane/ngx-datatable';
import { EmailService } from './email.service';
import { ToastrService } from 'ngx-toastr';
import { debounceTime } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { formatRelativeTime } from './helpers';
import Swal from "sweetalert2";
import { FilterStorageService } from 'app/shared/filter-service';

@Component({
  selector: 'app-email',
  templateUrl: './email.component.html',
  styleUrls: ['./email.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class EmailComponent implements OnInit {
  @ViewChild(DatatableComponent) table: DatatableComponent;
  @ViewChild('EmailManagementTable') EmailManagementTable: any;

  public breadcrumb: object;
  public ColumnMode = ColumnMode;
  public SelectionType = SelectionType;

  public typeLabelMap: { [key: string]: string } = {
    campaign_email: 'Campaign Email',
    transactional_email: 'Transactional Email'
  };

  public userId: number = null;
  public page: number = 1;
  public totalItem: number = 0;
  public search: String = null;
  public searchChanged = new Subject<string>();
  public typeFilter: String = null;
  public listEmail: any[] = [];

  constructor(
    private _filterStorage: FilterStorageService,
    private _emailService: EmailService,
    private _toastSerive: ToastrService,
    private _router: Router
  ) {
    this.page = this._filterStorage.get('email', 'page') ?? 1;
    this.search = this._filterStorage.get('email', 'search') ?? null;
    this.typeFilter = this._filterStorage.get('email', 'typeFilter') ?? null;
  }
  
  setBreadCrumb() {
    this.breadcrumb = {
      links: [
        {
          name: "Quản lý nội dung email",
          isHeader: true,
        }
      ],
    };
  }

  updateFilterStorage() {
    this._filterStorage.save("email", "page", this.page ?? null);
    this._filterStorage.save("email", "search", this.search ?? null);
    this._filterStorage.save("email", "typeFilter", this.typeFilter ?? null);
  }

  getListEmail() {
    this.updateFilterStorage();
    this._emailService.listEmail(this.userId, this.page, this.search, this.typeFilter).subscribe(
      (res) => {
        this.listEmail = res.results;
        this.totalItem = res.count;
      }
    )
  }

  onPage(event: any) {
    this.page = event.offset + 1;
    this.getListEmail();
  }

  searchDebounce() {
    this.searchChanged.pipe(
      debounceTime(500)
    ).subscribe((value) => {
      this.search = value;
      this.getListEmail();
    });
  }

  navigateEmail(id=null) {
    const segments = ['/super-admin/email_management/email_content'];
    if (id) segments.push(id);
    this._router.navigate(segments);
  }

  deleteEmail(row: any) {
    Swal.fire({
      title: `Bạn chắc chắn muốn xóa email ${row.code}-${row.title}?`,
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this._emailService.deleteEmail(row.id).subscribe(
          (res) => {
            this._toastSerive.success(`Đã xóa Email ${row.code}-${row.title}`, 'Thành công!');
            this.getListEmail();
          },
          (err) => {
            console.log(err)
          }
        )
      }
    });
  }

  onActivate(event) {
    if (event.type == 'click' && !['#', 'Hành động'].includes(event.column.name)) {
      this.navigateEmail(event.row.id);
    }
  }
  
  onSelect(event) {
    
  }

  formatRelativeTime = formatRelativeTime;

  ngOnInit(): void {
    this.setBreadCrumb();
    this.getListEmail();
    this.searchDebounce();
  }
}

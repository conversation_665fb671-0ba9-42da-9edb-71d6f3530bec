import {
  Directive,
  ElementRef,
  Input,
  OnChanges,
  Renderer2,
  SimpleChanges,
} from "@angular/core";

@Directive({
  selector: "[appExpandableText]",
})
export class ExpandableTextDirective implements OnChanges {
  @Input() text: string = "";
  @Input() maxWords: number = 35;
  @Input() isShowFull?: boolean; // undefined nếu không truyền

  private isExpanded = false;
  private button!: HTMLElement;

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes["text"] || changes["isShowFull"]) {
      this.render();
    }
  }

  private render() {
    if (!this.text) return;

    const words = this.text.trim().split(" ");
    const hasToggle = words.length > this.maxWords;

    // Nếu không truyền -> mặc định thu gọn, nếu truyền true -> full text
    this.isExpanded = this.isShowFull === true;

    this.el.nativeElement.innerHTML = this.isExpanded
      ? this.text
      : hasToggle
      ? words.slice(0, this.maxWords).join(" ") + "..."
      : this.text;

    if (hasToggle) {
      this.createButton();
    }
  }

  private createButton() {
    if (!this.button) {
      this.button = this.renderer.createElement("button");
      this.renderer.setStyle(this.button, "border", "none");
      this.renderer.setStyle(this.button, "background", "none");
      this.renderer.setStyle(this.button, "color", "#008fe3");
      this.renderer.setStyle(this.button, "cursor", "pointer");
      this.renderer.setStyle(this.button, "padding", "0");
      this.renderer.setStyle(this.button, "margin-left", "10px");

      this.renderer.listen(this.button, "click", (event) => {
        event.stopPropagation();
        this.toggleText();
      });
    }

    this.button.innerText = this.isExpanded ? "Thu gọn" : "Xem thêm";
    this.renderer.appendChild(this.el.nativeElement, this.button);
  }

  private toggleText() {
    this.isExpanded = !this.isExpanded;

    const words = this.text.trim().split(" ");
    this.el.nativeElement.innerHTML = this.isExpanded
      ? this.text
      : words.slice(0, this.maxWords).join(" ") + "...";

    this.button.innerText = this.isExpanded ? "Thu gọn" : "Xem thêm";
    this.renderer.appendChild(this.el.nativeElement, this.button);
  }
}

<div class="container p-0">
  <app-breadcrumb [breadcrumb]="breadcrumbDefault"></app-breadcrumb>
  <div class="card">
    <div class="card-body">
      <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div class="d-flex align-items-center">
            <img
              [src]="avatarUrl ? avatarUrl : 'assets/images/portrait/small/users.png'"
              alt="avatar"
              class="rounded-circle me-3 width-78px height-78px shadow-avatar cursor-pointer"
              ngbTooltip="Click để thay đổi ảnh đại diện"
              (click)="fileInput.click(); fileInput.value = ''"
            />
            <input
              #fileInput
              type="file"
              class="d-none"
              id="avatarInput"
              accept=".jpeg, .jpg, .png"
              (change)="onAvatarSelected($event)"
            />
            <div class="ml-2">
              <h5 class="mb-0">
                {{ userForm.get("fullname").value }}
                <span class="badge badge-pill badge-light-primary ms-2"
                  >Ngư<PERSON>i dùng</span
                >
              </h5>
              <div class="text-muted">{{ userForm.get("email").value }}</div>
            </div>
          </div>

          <!-- <button
            rippleEffect
            class="btn btn-outline-primary d-flex align-items-center text-primary"
          >
            <i data-feather="user-plus" class="mr-50"></i> Cấp quyền admin
          </button> -->
        </div>

        <form [formGroup]="userForm" autocomplete="off" (ngSubmit)="onSubmit()">
          <div class="card">
            <div class="card-body row p-0">
              <div class="col-md-6 col-12 mb-2" *ngIf="type == 'add'">
                <label>Email <span class="text-danger">*</span></label>
                <input
                  name="email"
                  autocomplete="off"
                  formControlName="email"
                  type="email"
                  class="form-control"
                  placeholder="Nhập"
                  appFormControlValidation
                />
              </div>

              <div class="col-md-6 col-12" *ngIf="type == 'add'">
                <label for="basic-default-password1"
                  >Mật khẩu <span class="text-danger">*</span></label
                >
                <div class="input-group input-group-merge form-password-toggle">
                  <input
                    [type]="mergedPwdShow ? 'text' : 'password'"
                    class="form-control"
                    name="password"
                    autocomplete="new-password"
                    formControlName="password"
                    placeholder="Nhập"
                  />
                  <div
                    class="input-group-append"
                    (click)="mergedPwdShow = !mergedPwdShow"
                  >
                    <span class="input-group-text cursor-pointer"
                      ><i
                        class="feather"
                        [ngClass]="{
                          'icon-eye-off': mergedPwdShow,
                          'icon-eye': !mergedPwdShow
                        }"
                      ></i
                    ></span>
                  </div>
                </div>
                <div class="col-sm-12 p-0">
                  <div
                    *ngIf="
                      (submitted || f.password.touched) && f.password.errors
                    "
                    class="invalid-form"
                  >
                    <small
                      *ngIf="f.password.errors['required']"
                      class="form-text text-danger"
                    >
                      Không được bỏ trống
                    </small>

                    <small
                      *ngIf="f.password.errors['minlength']"
                      class="form-text text-danger"
                    >
                      Mật khẩu phải có ít nhất 6 ký tự
                    </small>
                    <small
                      *ngIf="f.password.errors['maxLength']"
                      class="form-text text-danger"
                    >
                      Mật khẩu không quá 255 ký tự
                    </small>

                    <small
                      *ngIf="
                        f.password.errors['pattern'] &&
                        !f.password.errors['minlength']
                      "
                      class="form-text text-danger"
                    >
                      Mật khẩu phải có ít nhất 1 chữ in hoa và 1 ký tự đặc biệt
                    </small>
                  </div>
                </div>
              </div>

              <div class="col-md-6 col-12 mb-2">
                <label>Họ và tên <span class="text-danger">*</span></label>
                <input
                  formControlName="fullname"
                  type="text"
                  class="form-control"
                  appFormControlValidation
                  placeholder="Nhập"
                />
              </div>
              <div class="col-md-6 col-md-6 col-12 mb-2">
                <label>{{
                  role == "SUPER_ADMIN" ? "Tổ chức" : "Phòng ban"
                }}</label>
                <ng-select
                  [items]="listOrgan"
                  bindLabel="name"
                  bindValue="id"
                  placeholder="Chọn"
                  formControlName="organization"
                  (change)="changeOrganization($event)"
                >
                </ng-select>
              </div>
              <div class="col-md-6 col-md-6 col-12 mb-2">
                <label>Quyền</label>
                <ng-select
                  [items]="listRole"
                  bindLabel="label"
                  bindValue="id"
                  placeholder="Chọn"
                  formControlName="role"
                  [clearable]="false"
                >
                </ng-select>
              </div>
              <div class="col-md-6 col-md-6 col-12 mb-2">
                <label>Chức vụ</label>
                <ng-select
                  name="position"
                  autocomplete="off"
                  [items]="listPosition"
                  bindLabel="title"
                  bindValue="id"
                  placeholder="Chọn"
                  formControlName="position"
                >
                </ng-select>
              </div>
              <div class="col-md-6 col-12 mb-2">
                <label>Giới tính</label>
                <ng-select
                  [items]="gender"
                  placeholder="Chọn"
                  formControlName="gender"
                >
                </ng-select>
              </div>
              <div class="col-md-6 col-12 mb-2">
                <label>Ngày sinh</label>
                <ng2-flatpickr
                  #flatpickrRef
                  [config]="basicDateOptions"
                  name="customDate"
                  placeholder="dd/mm/yyyy"
                  formControlName="dob"
                ></ng2-flatpickr>
              </div>
              <div class="col-md-6 col-12 mb-2">
                <label>Số điện thoại</label>
                <input
                  [appFormControlValidation]="
                    'Số điện thoại gồm 10 chữ số và phải bắt đầu từ số 0'
                  "
                  formControlName="phone"
                  type="number"
                  class="form-control"
                  placeholder="Nhập"
                />
              </div>

              <div class="col-md-6 col-12 mb-2">
                <label>Địa chỉ</label>
                <input
                  formControlName="address"
                  type="text"
                  class="form-control"
                  placeholder="Nhập"
                  appFormControlValidation
                />
              </div>

              <div class="d-flex justify-content-end w-100 px-1">
                <button
                  type="button"
                  class="btn btn-secondary mx-2"
                  (click)="onCancel()"
                >
                  Hủy
                </button>
                <button type="submit" class="btn btn-primary">
                  {{ type == "add" ? "Thêm người dùng" : "Lưu chỉnh sửa" }}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

import {
  Component,
  Input,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import { Form<PERSON>uilder, FormGroup, NgForm, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { FlatpickrOptions } from "ng2-flatpickr";
import { ToastrService } from "ngx-toastr";
import { firstValueFrom, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { CampaignService } from "../campaign.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
  RecipientPickerModalComponent,
  TreeNode,
} from "./recipient-picker-modal/recipient-picker-modal.component";
import { EmailService } from "../../email/email.service";
import { FormatTime } from "../../../../../../../util/formatTime";
import { CampaignStatus } from "app/models/StatusCampaign";
import Swal from "sweetalert2";

@Component({
  selector: "app-campaign-control",
  templateUrl: "./campaign-control.component.html",
  styleUrls: ["./campaign-control.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class CampaignControlComponent implements OnInit {
  @ViewChild("formDirective") private formDirective: NgForm;
  @ViewChild("flatpickrRef", { static: false }) flatpickrElement: any;
  public breadcrumbDefault: object;
  public type: string;
  public basicDateOptions: FlatpickrOptions = {
    altInput: true,
    altFormat: "d-m-Y H:i:S",
    dateFormat: "Y-m-d H:i:S",
    enableTime: true,
    time_24hr: true,
    enableSeconds: true,
    mode: "single",
  };
  public submitted: boolean = false;
  public formData: FormData;
  public _unSubAll: Subject<any> = new Subject();
  public campaignId: string;
  public codeValue = "";
  public nameValue = "";
  public timeValue: any = null;
  public emailTemplate: any = "";
  public descriptionValue: any = "";
  public danhSachNguoiNhan: any = [];
  public recipientSearch = "";
  public campaignForm: FormGroup;
  public listTargetUsers: any = [];
  public listTargetOrganizations: any = [];
  public listAllOrganizations: any = [];
  public listAllUsers: any = [];
  public preselectedIds: Array<string | number> = [];
  public orgTree: TreeNode[] = [];
  public templateOptions: any = [];
  public payloadSubmit: any = {};
  public listTarget: any = [];
  public idCampaign: any = "";
  public status: any = "";
  public campaignStatus = CampaignStatus;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private _campaignService: CampaignService,
    private modal: NgbModal,
    private toastSerive: ToastrService,
    private _emailService: EmailService
  ) {}

  ngOnInit(): void {
    const currentUser = JSON.parse(localStorage.getItem("current_User"));
    const user = currentUser.id;
    this.type = this.route.snapshot.queryParams.type;

    this.breadcrumbDefault = {
      links: [
        {
          name: "Quản lý chiến dịch",
          // isHeader: true,
          isLink: true,
          link: '/super-admin/campaign'
        },
        {
          name: this.type == "add" ? "Thêm chiến dịch" : "Cập nhật chiến dịch",
        },
      ],
    };
    this._emailService.listEmail(null, null, null, null).subscribe(
      (res) => {
        this.templateOptions = (res as any[]).map((e) => ({
          id: e.id,
          label: `${e.code} - ${e.title}`,
        }));
        // console.log("res", res);
      },
      (error) => {}
    );
    this.getOrgTree();
    this.campaignForm = this.fb.group({
      code: ["", Validators.required],
      name: ["", Validators.required],
      email: [[], Validators.required],
      description: ["", Validators.required],
      time: [null, Validators.required],
    });
    // console.log(this.campaignForm.value);
    this._campaignService.data
      .pipe(takeUntil(this._unSubAll))
      .subscribe((res) => {
        if (res) {
          this.idCampaign = res.id;
          this.status = res.status;
          this.campaignForm = this.fb.group({
            code: [res.code, Validators.required],
            name: [res.name, Validators.required],
            email: [res.email, Validators.required],
            description: [res.description, Validators.required],
            time: [new Date(res.time), Validators.required],
          });

          if ([this.campaignStatus.STATUS_ONGOING, this.campaignStatus.STATUS_DONE].includes(this.status)) {
            this.campaignForm.get('code')?.disable();
            this.campaignForm.get('name')?.disable();
            this.campaignForm.get('email')?.disable();
            this.campaignForm.get('description')?.disable();
            this.campaignForm.get('time')?.disable();
            this.basicDateOptions = {
              ...this.basicDateOptions,
              clickOpens: false, // chặn mở popup chọn ngày
              allowInput: false  // chặn gõ text
            };
          }

          this.basicDateOptions.defaultDate = new Date(res.time);

          if (res.users_info) {
            this.danhSachNguoiNhan = (res.users_info as any[])
              .filter((n) => n?.active === true)
              .map((n) => ({
                id: `${
                  n.organization_memberships.length > 0
                    ? n.organization_memberships[0].organization_id
                    : ""
                }-u-${n.id} `,
                name: n.fullname,
                email: n.email,
                type: "user",
                organization_name:
                  n.organization_memberships.length > 0
                    ? n.organization_memberships[0].organization_name
                    : "",
                user_id: n.id,
                organization_id:
                  n.organization_memberships.length > 0
                    ? n.organization_memberships[0].organization_id
                    : "",
              }));
          }
        }
      });
    this.attachParent(this.orgTree);
    this.applyPreselected();
  }
  private attachParent(nodes: TreeNode[], parent?: TreeNode) {
    for (const n of nodes || []) {
      n.parent = parent;
      n.expanded = n.expanded ?? true;
      if (n.children?.length) this.attachParent(n.children, n);
    }
  }
  private parseServerTime(
    value: string | number | Date | null | undefined
  ): Date | null {
    if (!value) return null;
    if (value instanceof Date) return value;

    if (typeof value === "number") {
      const ms = value < 1e12 ? value * 1000 : value;
      const d = new Date(ms);
      return isNaN(d.getTime()) ? null : d;
    }

    if (typeof value === "string") {
      const s = value.trim();

      if (/^\d+$/.test(s)) {
        const n = parseInt(s, 10);
        const ms = n < 1e12 ? n * 1000 : n;
        const d = new Date(ms);
        return isNaN(d.getTime()) ? null : d;
      }

      let d = new Date(s);
      if (!isNaN(d.getTime())) return d;

      d = new Date(s.replace(" ", "T"));
      if (!isNaN(d.getTime())) return d;
    }

    return null;
  }

  private toDatetimeLocalInput(
    value: string | number | Date | null | undefined
  ): string {
    const d = this.parseServerTime(value);
    if (!d) return "";
    const pad = (n: number) => String(n).padStart(2, "0");
    const yyyy = d.getFullYear();
    const mm = pad(d.getMonth() + 1);
    const dd = pad(d.getDate());
    const hh = pad(d.getHours());
    const mi = pad(d.getMinutes());
    return `${yyyy}-${mm}-${dd}T${hh}:${mi}`;
  }
  copyId() {
    if (!this.codeValue) return;
    navigator.clipboard.writeText(this.codeValue);
    this.toastSerive.info(`Đã copy "${this.codeValue}"`)
  }
  onIdValueChange(event: any) {
    this.codeValue = (event.target.value || "").trim();
  }

  get filteredRecipients() {
    const q = (this.recipientSearch || "").toLowerCase().trim();
    const src = this.danhSachNguoiNhan || [];
    if (!q) return src;
    return src.filter(
      (r) =>
        (r.name || "").toLowerCase().includes(q) ||
        String(r.id).toLowerCase().includes(q)
    );
  }
  onAddRecipient() {
    const ref = this.modal.open(RecipientPickerModalComponent, {
      size: "lg",
      centered: true,
      backdrop: "static",
    });
    ref.componentInstance.data = this.orgTree;
    ref.componentInstance.preselectedIds = (
      this.danhSachNguoiNhan as any[]
    ).map((p) => String(p.id).trim());

    ref.result
      .then((picked) => {
        if (!picked) return;
        if (
          Array.isArray(picked) &&
          picked.length &&
          (typeof picked[0] === "string" || typeof picked[0] === "number")
        ) {
          const idSet = new Set(picked);
          this.danhSachNguoiNhan = this.flattenUsers(this.orgTree).filter((u) =>
            idSet.has(u.id)
          );
          return;
        }

        this.danhSachNguoiNhan = (picked as any[])
          .filter((n) => n?.type === "user")
          .map((n) => ({
            id: n.id,
            name: n.name,
            email: n.email,
            type: n.type,
            organization_name: n.organization_name,
            user_id: n.user_id,
            organization_id: n.organization_id,
          }));
      })
      .catch(() => {});
  }

  private applyPreselected() {
    if (!this.orgTree?.length || !this.preselectedIds?.length) return;
    const idSet = new Set(this.preselectedIds.map((v) => String(v)));

    const walk = (nodes: TreeNode[]) => {
      for (const n of nodes || []) {
        if (n.type === "user" && idSet.has(String(n.id))) {
          n.checked = true;
          n.partial = false;
          this.setUp(n.parent);
        }
        if (n.children?.length) walk(n.children);
      }
    };
    walk(this.orgTree);
  }
  private setUp(n?: TreeNode) {
    if (!n) return;
    const kids = n.children ?? [];
    const all = kids.every((k) => k.checked);
    const none = kids.every((k) => !k.checked && !k.partial);
    n.checked = all;
    n.partial = !all && !none;
    this.setUp(n.parent);
  }
  getAllOrganization() {
    const params = {};
    this._campaignService.getAllOrganization(params).subscribe((res) => {
      this.listAllOrganizations = res;
    });
  }
  getOrgTree() {
    const params = {};
    this._campaignService.getOrgTree(params).subscribe((res) => {
      this.orgTree = res;
      // this.listAllOrganizations = res;
    });
  }
  getPayload(status: CampaignStatus = this.campaignStatus.STATUS_SCHEDULED) {
    const currentUser = JSON.parse(localStorage.getItem("current_User"));
    const user = currentUser.id;
    if (!user) {
      return;
    }
    const raw = this.campaignForm.get("time")?.value;
    const pickedOnly = raw ? new Date(raw) : new Date();
    const today = new Date();

    let finalStatus = status;
    if (this.idCampaign && status == this.campaignStatus.STATUS_DRAFT) {
      finalStatus = this.campaignStatus.STATUS_SCHEDULED;
    }
    const target_organizations = this.danhSachNguoiNhan.map(
      (x: any) => x.organization_id
    );
    const target_users = this.danhSachNguoiNhan.map((x: any) => x.user_id);
    this.payloadSubmit = {
      ...this.campaignForm.value,
      target_organizations,
      target_users,
      user,
      status: finalStatus,
      time: pickedOnly.toISOString(),
    };
  }
  async validateForm(): Promise<boolean> {
    if (this.campaignForm.invalid) {
      this.campaignForm.markAllAsTouched();
      return false;
    }
    const codeCtrl = this.campaignForm.get("code");
    if (!codeCtrl.value) {
      this.toastSerive.error("Mã chiến dịch không được để trống", "Thất bại", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
      return false;
    }

    const params: any = { code: codeCtrl.value };
    if (this.idCampaign) params.exclude_id = this.idCampaign;

    try {
      const res: any = await firstValueFrom(
        this._campaignService.checkExistCode(params)
      );
      if (res?.exists === true) {
        this.toastSerive.error("Mã chiến dịch đã tồn tại", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        return false;
      } else {
      }
    } catch (e) {
      console.error("Check code error:", e);
      return false;
    }

    if ((this.danhSachNguoiNhan as any[]).length === 0) {
      this.toastSerive.error("Vui lòng thêm danh sách người nhận", "Thất bại", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
      return false;
    }

    return true;
  }

  async saveDraft() {
    if (!(await this.validateForm())) return;

    this.getPayload(this.campaignStatus.STATUS_DRAFT);
    this._campaignService.createCampaign(this.payloadSubmit).subscribe(
      (res) => {
        this.toastSerive.success(
          "Lưu nháp chiến dịch thành công",
          "Thành công",
          {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          }
        );
        this.onCancel();
      },
      (error) => {
        this.toastSerive.error("Lưu nháp chiến dịch thất bại", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
  async onSubmit() {
    if (!(await this.validateForm())) return;
    this.getPayload();
    this._campaignService.createCampaign(this.payloadSubmit).subscribe(
      (res) => {
        this.toastSerive.success("Thêm chiến dịch thành công.", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        this.onCancel();
      },
      (error) => {
        this.toastSerive.error("Thêm chiến dịch thất bại", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }

  async updateCampaign() {
    if (!(await this.validateForm())) return;
    if (this.status === this.campaignStatus.STATUS_DRAFT) {
      const result = await Swal.fire({
        title: "Xác nhận",
        text: "Chiến dịch sẽ được lên lịch gửi theo cấu hình và không còn ở trạng thái nháp. Bạn có chắc chắn muốn tiếp tục?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonText: "Có, tiếp tục",
        cancelButtonText: "Hủy",
        reverseButtons: true,
      });

      if (!result.isConfirmed) {
        return;
      }
    }
    this.getPayload();
    this._campaignService
      .updateCampaign(this.idCampaign, this.payloadSubmit)
      .subscribe(
        (res) => {
          this.toastSerive.success(
            "Cập nhật chiến dịch thành công.",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
          this.onCancel();
        },
        (error) => {
          this.toastSerive.error("Cập nhật chiến dịch thất bại", "Thất bại", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      );
  }
  pageSize = 5;
  page = 1;

  get totalFiltered(): number {
    return this.filteredRecipients.length;
  }

  get totalPages(): number {
    return Math.max(1, Math.ceil(this.totalFiltered / this.pageSize));
  }

  get pagedRecipients() {
    const start = (this.page - 1) * this.pageSize;
    return this.filteredRecipients.slice(start, start + this.pageSize);
  }

  get pageList(): number[] {
    const total = this.totalPages;
    const spread = 5;
    let start = Math.max(1, this.page - 2);
    let end = Math.min(total, start + spread - 1);
    if (end - start < spread - 1) start = Math.max(1, end - spread + 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  goToPage(p: number) {
    this.page = Math.min(Math.max(1, p), this.totalPages);
  }
  prev() {
    this.goToPage(this.page - 1);
  }
  next() {
    this.goToPage(this.page + 1);
  }

  removeRecipient(r: any) {
    this.danhSachNguoiNhan = (this.danhSachNguoiNhan || []).filter(
      (x) => x.id !== r.id
    );
    const maxPage = this.totalPages;
    if (this.page > maxPage) this.page = maxPage;
  }

  trackById = (_: number, item: any) => item.id;

  private flattenUsers(
    nodes: any[]
  ): Array<{ id: any; name?: string; email?: string; type?: string }> {
    const out: Array<{
      id: any;
      name?: string;
      email?: string;
      type?: string;
    }> = [];
    const walk = (ns: any[]) => {
      for (const n of ns || []) {
        if (n?.type === "user")
          out.push({ id: n.id, name: n.name, email: n.email, type: n.type });
        if (n?.children?.length) walk(n.children);
      }
    };
    walk(nodes);
    return out;
  }
  get f() {
    return this.campaignForm.controls;
  }

  onCancel() {
    this.router.navigate(["super-admin/campaign"]);
  }

  ngOnDestroy() {
    this._unSubAll.next(null);
    this._unSubAll.complete();
    this._campaignService.data.next(null);
  }
}
